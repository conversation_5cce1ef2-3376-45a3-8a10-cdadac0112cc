import { DEFAULT_DATE_FORMAT } from '@config/common';
import dayjs, { type ConfigType } from 'dayjs';

type FormatNumberParams = {
  value: Nullish<number>;
  toFixed?: number;
  locale?: string;
  minimumFractionDigits?: number;
};

export const formatNumber = ({
  value,
  toFixed = 2,
  locale,
  minimumFractionDigits,
}: FormatNumberParams): string =>
  value
    ? Number(value.toFixed(toFixed)).toLocaleString(locale, {
        minimumFractionDigits,
      })
    : '0';

export const formatDate = (date: ConfigType) =>
  dayjs(date).format(DEFAULT_DATE_FORMAT);
