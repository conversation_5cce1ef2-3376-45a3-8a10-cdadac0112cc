if (typeof importScripts === 'function') {
  importScripts(
    'https://www.gstatic.com/firebasejs/10.10.0/firebase-app-compat.js',
  );
  importScripts(
    'https://www.gstatic.com/firebasejs/10.10.0/firebase-messaging-compat.js',
  );
  const firebaseApp = firebase.initializeApp({
    apiKey: process.env.VITE_FIREBASE_API_KEY,
    authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.VITE_FIREBASE_APP_ID,
  });

  const messaging = firebase.messaging(firebaseApp);

  messaging.onBackgroundMessage((payload) => {
    const notificationTitle = payload.notification.title;

    const notificationOptions = {
      body: payload.notification.body,
      icon: payload.notification.image,
    };

    self.registration.showNotification(notificationTitle, notificationOptions);
  });
}
