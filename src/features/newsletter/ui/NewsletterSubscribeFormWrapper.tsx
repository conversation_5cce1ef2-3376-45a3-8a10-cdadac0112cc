import { Form } from '@components/ui/form';
import { userApi, useUserNewsletterSubscriptionInfo } from '@entities/user';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@hooks/system';
import { useQueryClient } from '@tanstack/react-query';
import { processGqlFormValidationErrors } from '@utils/parseGraphQLError';
import { cn } from '@utils/tailwind';
import { type ReactNode, useCallback } from 'react';
import { useForm, type UseFormReturn } from 'react-hook-form';
import * as z from 'zod';

const NewsletterSubscribeFormSchema = z.object({
  phone: z.string(),
  email: z.string(),
});

export type NewsletterSubscribeFormType = z.infer<
  typeof NewsletterSubscribeFormSchema
>;

type NewsletterSubscribeWrapperProps = {
  className?: string;
  onCompleted: () => void;
  children: (data: {
    form: UseFormReturn<NewsletterSubscribeFormType>;
  }) => ReactNode;
};

export const NewsletterSubscribeFormWrapper = ({
  children,
  onCompleted,
  className,
}: NewsletterSubscribeWrapperProps) => {
  const { data: userNewsletterSubscriptionInfo } =
    useUserNewsletterSubscriptionInfo();
  const queryClient = useQueryClient();
  const { showErrorMessage } = useToast();

  const form = useForm<NewsletterSubscribeFormType>({
    resolver: zodResolver(NewsletterSubscribeFormSchema),
    defaultValues: {
      phone: userNewsletterSubscriptionInfo?.phone ?? '',
      email: userNewsletterSubscriptionInfo?.email ?? '',
    },
  });

  const updateUserNewsletterAgreementMutation = userApi.useUpdateUserMutation({
    onSuccess: async () => {
      setTimeout(async () => {
        await queryClient.invalidateQueries({
          queryKey: userApi.useSuspenseUserQuery.getKey(),
        });
      }, 1500);
    },
  });

  const handleFormSubmit = useCallback(
    async (data: NewsletterSubscribeFormType) => {
      if (!userNewsletterSubscriptionInfo?.id)
        throw new Error('Cannot subscribe to newsletter without user');

      try {
        await updateUserNewsletterAgreementMutation.mutateAsync({
          userId: userNewsletterSubscriptionInfo.id,
          email: data.email,
          phone: data.phone,
          newsletterAgreement: true,
        });

        onCompleted();
      } catch (error) {
        if (Array.isArray(error)) {
          processGqlFormValidationErrors({
            error,
            setFormError: form.setError,
          });
          return;
        }

        showErrorMessage();
      }
    },
    [
      userNewsletterSubscriptionInfo?.id,
      updateUserNewsletterAgreementMutation.mutateAsync,
      form.setError,
      showErrorMessage,
      onCompleted,
    ],
  );

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleFormSubmit)}
        className={cn('w-full', className)}
      >
        {children({
          form,
        })}
      </form>
    </Form>
  );
};
