import { v4 as createId } from 'uuid';

import { cn } from '@/shared/utils/tailwind';

type ProgressDotsProps = {
  dots: number;
  current: number;
  onProgressDotClick: (index: number) => () => void;
  className: string;
};

export const ProgressDots = ({
  dots,
  current,
  onProgressDotClick,
  className,
}: ProgressDotsProps) => {
  return (
    <div className={cn(className)}>
      <div className="flex">
        {Array.from({ length: dots }).map((_, index) => (
          <button
            type="button"
            key={createId()}
            className="cursor-pointer p-[5px]"
            onClick={onProgressDotClick(index)}
          >
            <div
              className={cn(
                'h-[12px] w-[12px] rounded-full bg-primary-white',
                index !== current && 'opacity-50',
              )}
            />
          </button>
        ))}
      </div>
    </div>
  );
};
