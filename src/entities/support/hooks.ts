import { APP_COUNTRY, VITE_ZENDESK_CHAT_KEY_CODE } from '@config/envs';
import { isAppLanguage } from '@utils/isAppLanguage';
import { isSupportedCountry } from '@utils/isSupportedCountry';
import i18n from 'i18next';

import { AppLanguage, SupportedCountries } from '@/shared/types';

import { ZENDESK_CHAT_CONFIG_BY_COUNTRIES_AND_LANGUAGES_MAP } from './config';

export const useZendeskWidget = () => {
  const changeZendeskLocale = (locale: string): void => {
    if (!VITE_ZENDESK_CHAT_KEY_CODE) return;

    window.zE('webWidget', 'setLocale', locale);
    setZendeskChatSettings(locale);
  };

  const setZendeskChatSettings = (locale: string): void => {
    const appCountry = isSupportedCountry(APP_COUNTRY)
      ? APP_COUNTRY
      : SupportedCountries.EE;
    const appLanguage = isAppLanguage(locale) ? locale : AppLanguage.EN;

    const settings =
      ZENDESK_CHAT_CONFIG_BY_COUNTRIES_AND_LANGUAGES_MAP[appCountry][
        appLanguage
      ];

    window.zE('webWidget', 'updateSettings', {
      webWidget: settings,
    });
  };

  const loadZendeskWidget = (locale = i18n.language): void => {
    if (!VITE_ZENDESK_CHAT_KEY_CODE) return;

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.id = 'ze-snippet';
    script.async = true;
    script.src = `https://static.zdassets.com/ekr/snippet.js?key=${VITE_ZENDESK_CHAT_KEY_CODE}`;
    script.addEventListener('load', () => {
      changeZendeskLocale(locale);
      setZendeskChatSettings(locale);
    });
    document.getElementsByTagName('body')[0].appendChild(script);
  };

  return {
    loadZendeskWidget,
    changeZendeskLocale,
  };
};
