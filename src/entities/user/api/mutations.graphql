mutation UpdateUser(
  $userId: Int!
  $email: String
  $phone: String
  $address: String
  $postCode: String
  $city: String
  $politicalExposure: PoliticalExposure
  $occupationCategory: OccupationCategory
  $employmentDate: String
  $iban: String
  $netIncomeMonthly: Float
  $numberOfDependents: Int
  $monthlyLivingExpenses: Float
  $expenditureMonthly: Float
  $planningNewDebts: Float
  $futureReducedEarnings: Float
  $ultimateBeneficialOwner: Boolean
  $newsletterAgreement: Boolean
  $overdueDebt: Float
) {
  update_user(
    user_id: $userId
    email: $email
    phone: $phone
    newsletter_agreement: $newsletterAgreement
    address: $address
    post_code: $postCode
    city: $city
    occupation_category: $occupationCategory
    employment_date: $employmentDate
    iban: $iban
    net_income_monthly: $netIncomeMonthly
    number_of_dependents: $numberOfDependents
    monthly_living_expenses: $monthlyLivingExpenses
    expenditure_monthly: $expenditureMonthly
    political_exposure: $politicalExposure
    planning_new_debts: $planningNewDebts
    future_reduced_earnings: $futureReducedEarnings
    ultimate_beneficial_owner: $ultimateBeneficialOwner
    overdue_debt: $overdueDebt
  ) {
    id
  }
}
