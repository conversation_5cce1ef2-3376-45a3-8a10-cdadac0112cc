import { ProgressBarWithLabel } from '@components/ProgressBarWithLabel';
import { Typography } from '@components/typography';
import { progressVariants } from '@components/ui/progress';
import { Skeleton } from '@components/ui/skeleton';
import {
  LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { formatNumber } from '@utils/formatters';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import type { CreditAccount } from '@/shared/types';

import { WITHDRAWAL_PANEL_TEST_KEYS } from '../../config';
import styles from './CreditLineCard.module.css';

export type CreditLineCardProps = {
  creditLimit: CreditAccount['credit_limit'];
  unpaidPrincipal: CreditAccount['unpaid_principal'];
  after?: React.ReactNode;
  title?: string;
  ModificationCtaButton?: React.ReactNode;
  LimitIncreaseCtaButton?: React.ReactNode;
};

export const CreditLineCard: FC<CreditLineCardProps> = ({
  creditLimit,
  unpaidPrincipal,
  after,
  title,
  ModificationCtaButton,
  LimitIncreaseCtaButton,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLineBalanceWidget);

  const allowedPrincipal = creditLimit - unpaidPrincipal;

  const cardTitle = title ?? t(LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.title);

  return (
    <div
      className={styles.container}
      data-with-cta={!!ModificationCtaButton || !!LimitIncreaseCtaButton}
      data-testid={WITHDRAWAL_PANEL_TEST_KEYS.container}
    >
      <div className={styles.info}>
        <Typography
          testId={WITHDRAWAL_PANEL_TEST_KEYS.balance}
          variant="text-s"
        >
          {cardTitle}
        </Typography>
        <Typography
          testId={WITHDRAWAL_PANEL_TEST_KEYS.balanceValue}
          variant="m"
        >
          {Math.floor(allowedPrincipal).toLocaleString()} €
        </Typography>
      </div>
      <ProgressBarWithLabel
        maxValue={creditLimit}
        minValue={0}
        testId={WITHDRAWAL_PANEL_TEST_KEYS.progressBar}
        value={allowedPrincipal}
        maxLabel={`${formatNumber({ value: creditLimit })} €`}
        className={styles.progress}
        minLabel={t(
          LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.signedCaAmountMinLabel,
        )}
      />
      {after && <div className={styles.after}>{after}</div>}
      {ModificationCtaButton || LimitIncreaseCtaButton ? (
        <div className={styles.ctaContainer}>
          {LimitIncreaseCtaButton && LimitIncreaseCtaButton}
          {ModificationCtaButton && ModificationCtaButton}
        </div>
      ) : null}
    </div>
  );
};

export const CreditLineCardSkeleton = () => (
  <Skeleton
    className={cn(
      styles.container,
      'rounded-3xl border-none bg-primary-white px-6 py-8 md:bg-muted md:p-8 md:pb-14',
    )}
  >
    <div className={styles.info}>
      <Skeleton className="h-[1.25rem] w-56 bg-neutral-200" />
      <Skeleton className="h-[2.5rem] w-36 bg-neutral-200" />
    </div>
    <Skeleton
      className={cn(
        styles.after,
        'h-12 w-full rounded-full bg-neutral-200 md:w-28',
      )}
    />
    <Skeleton
      className={cn(
        styles.progress,
        progressVariants({
          className: 'bg-gray-300',
        }),
      )}
    />
  </Skeleton>
);
