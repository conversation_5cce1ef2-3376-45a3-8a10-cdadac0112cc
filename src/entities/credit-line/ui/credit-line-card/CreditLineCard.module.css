.container {
  @apply grid 
    grid-areas-[info,progress,after] 
    data-[with-cta=true]:grid-areas-[info,progress,after,ctaContainer]
    grid-cols-1 
    items-center 
    gap-x-4 
    gap-y-6
    pb-[4.5rem] 
    bg-primary-white 
    md:grid-cols-[1fr,auto] 
    md:rounded-3xl 
    md:border 
    md:border-neutral-200 
    md:p-8 
    md:grid-areas-[info_after,progress_progress]
    md:data-[with-cta=true]:grid-areas-[info_after,progress_progress,ctaContainer_ctaContainer];
}

.info {
  @apply grid-in-[info] 
    grid 
    gap-1.5;
}

.progress {
  @apply grid-in-[progress];
}

.after {
  @apply grid-in-[after] 
    mt-2 
    md:mt-0;
}

.ctaContainer {
  @apply grid 
    gap-2 
    md:col-span-2;
}
