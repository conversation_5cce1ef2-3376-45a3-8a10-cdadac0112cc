import { CreditAccountLimitIncreaseButton } from '@features/credit-account-limit-increase/ui/CreditAccountLimitIncreaseButton';
import { CreditAccountModificationButton } from '@features/credit-account-modification/ui/CreditAccountModificationButton';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';

vi.mock(
  '@features/credit-account-modification/ui/CreditAccountModificationButton',
);

vi.mock(
  '@features/credit-account-limit-increase/ui/CreditAccountLimitIncreaseButton',
);

const queryClient = new QueryClient();

const mock = {
  creditLimit: 3000,
  unpaidPrincipal: 1000,
} as const;

import { WITHDRAWAL_PANEL_TEST_KEYS } from '../config';
import { CreditLineCard } from '../ui/credit-line-card';

describe('WithdrawalCard', () => {
  it('should render', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <CreditLineCard
          after={
            <div data-testid={WITHDRAWAL_PANEL_TEST_KEYS.after}>After</div>
          }
          creditLimit={mock.creditLimit}
          unpaidPrincipal={mock.unpaidPrincipal}
          LimitIncreaseCtaButton={null}
          ModificationCtaButton={null}
        />
      </QueryClientProvider>,
    );

    expect(
      screen.getByTestId(WITHDRAWAL_PANEL_TEST_KEYS.container),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(WITHDRAWAL_PANEL_TEST_KEYS.balance),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(WITHDRAWAL_PANEL_TEST_KEYS.balanceValue),
    ).toHaveTextContent(
      (mock.creditLimit - mock.unpaidPrincipal).toLocaleString(),
    );
    expect(
      screen.getByTestId(WITHDRAWAL_PANEL_TEST_KEYS.progressBar),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(WITHDRAWAL_PANEL_TEST_KEYS.after),
    ).toBeInTheDocument();
  });

  it('should render with limit increase CTA', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <CreditLineCard
          unpaidPrincipal={mock.unpaidPrincipal}
          creditLimit={mock.creditLimit}
          LimitIncreaseCtaButton={<CreditAccountLimitIncreaseButton />}
          ModificationCtaButton={null}
        />
      </QueryClientProvider>,
    );

    expect(CreditAccountLimitIncreaseButton).toHaveBeenCalled();

    expect(
      screen.getByTestId(WITHDRAWAL_PANEL_TEST_KEYS.container),
    ).toBeInTheDocument();
  });

  it('should render with CA modification CTA', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <CreditLineCard
          unpaidPrincipal={mock.unpaidPrincipal}
          creditLimit={mock.creditLimit}
          LimitIncreaseCtaButton={null}
          ModificationCtaButton={<CreditAccountModificationButton />}
        />
      </QueryClientProvider>,
    );

    expect(CreditAccountModificationButton).toHaveBeenCalled();

    expect(
      screen.getByTestId(WITHDRAWAL_PANEL_TEST_KEYS.container),
    ).toBeInTheDocument();
  });

  it('should render with all CTAs', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <CreditLineCard
          unpaidPrincipal={mock.unpaidPrincipal}
          creditLimit={mock.creditLimit}
          LimitIncreaseCtaButton={<CreditAccountLimitIncreaseButton />}
          ModificationCtaButton={<CreditAccountModificationButton />}
        />
      </QueryClientProvider>,
    );

    expect(CreditAccountModificationButton).toHaveBeenCalled();
    expect(CreditAccountLimitIncreaseButton).toHaveBeenCalled();
  });
});
