import { creditAccountLimitRecalculationApi } from './api';

export const useGetCreditAccountLimitRecalculationData = (hash: string) => {
  return creditAccountLimitRecalculationApi.useGetCreditAccountLimitRecalculationQuery(
    { hash },
    {
      enabled: !!hash,
      select: (data) => {
        if (!data.creditAccountLimitRecalculation) {
          throw new Error('Credit account limit recalculation data is missing');
        }
        const {
          current_credit_limit: currentCreditLimit,
          new_credit_limit: newCreditLimit,
        } = data.creditAccountLimitRecalculation;
        const creditLimitIncreaseAmount =
          (newCreditLimit ?? 0) - (currentCreditLimit ?? 0);
        return {
          creditLimitIncreaseAmount,
          newCreditLimit,
          currentCreditLimit,
        };
      },
    },
  );
};
