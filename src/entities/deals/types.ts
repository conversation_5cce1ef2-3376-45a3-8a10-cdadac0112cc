import type { AppLanguage } from '@/shared/types';

export enum DealId {
  MOKYKLINE = 'mokykline',
  WOLT_LV = 'wolt-lv',
  WOLT_LT = 'wolt-lt',
  UPGREAT_EE = 'upgreat-ee',
  UPGREAT_LV = 'upgreat-lv',
  UPGREAT_LT = 'upgreat-lt',
  FITLAP = 'fitlap',
  KULDAN = 'kuldan',
  MLILY_EE = 'mlily-ee',
  GIVEN_EE = 'given-ee',
  GOLDEN_LOMBARD_EE = 'golden-lombard-ee',
  MADRATSIPOOD_EE = 'madratsipood-ee',
  AIRSOFTGO_EE = 'airsoftgo-ee',
  MSL_EE = 'msl-ee',
  ROKOKO_LV = 'rokoko-lv',
  EKOPLANET_EE = 'ekoplanet-ee',
  EKOPLANET_LV = 'ekoplanet-lv',
  EKOPLANET_LT = 'ekoplanet-lt',
  ELIX_HEALTH_EE = 'elix-health-ee',
  BILANCE = 'bilance',

  MEREVARUSTUS_EE = 'merevarustus-ee',
  INPUIT_EE = 'inpuit-ee',
  ANIX_SHOP_EE = 'anix-shop-ee',
  LIGHT_CONCEPT_EE = 'light-concept-ee',
  ALEXANTO_EE = 'alexanto-ee',

  BIG_ECO_1 = 'big-eco-1',
  BIG_ECO_2 = 'big-eco-2',
  JYSK_EE = 'jysk-ee',
  DETAILER_PLACE_EE = 'detailer-place-ee',
  DETAILER_PLACE_LV = 'detailer-place-lv',
  DETAILER_PLACE_LT = 'detailer-place-lt',
}

export type Deal = {
  id: DealId;
  brand: string;
  title: string;
  description: string;
  discountCode?: string;
  discount: string;
  discountDescription?: string;
  imageSrc: string;
  websiteUrl: string | Record<AppLanguage, string>;
};
