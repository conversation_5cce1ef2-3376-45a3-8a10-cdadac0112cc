import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { useGetDeal } from '../hooks';
import type { DealId } from '../types';
import styles from './DealCard.module.css';

type DealCardProps = {
  dealId: DealId;
  className?: string;
};

export const DealCard: FC<DealCardProps> = ({ className, dealId }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const getDeal = useGetDeal();
  const { brand, title, discount, imageSrc } = getDeal(dealId);

  return (
    <div className={cn(styles.container, 'group', className)}>
      <div
        className={cn(styles.card, 'group-hover:scale-105')}
        style={{
          background: `linear-gradient(180deg, rgba(0, 0, 0, 0) 41.79%, rgba(0, 0, 0, 0.75) 100%),
				 url(${imageSrc}) lightgray 50% / cover no-repeat`,
        }}
      >
        <Typography variant="text-s" className={styles.brand} tag="span">
          {t(brand)}
        </Typography>
        <Typography
          variant="xs"
          className={styles.discount}
          tag="span"
          affects="semibold"
        >
          {t(discount)}
        </Typography>
      </div>
      <Typography variant="text-l" affects="semibold" className="line-clamp-2">
        {t(title)}
      </Typography>
    </div>
  );
};
