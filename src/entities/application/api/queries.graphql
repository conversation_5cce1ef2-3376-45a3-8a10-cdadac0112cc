query Application($referenceKey: String) {
  application_by_reference(reference_key: $referenceKey) {
    id
    merchant_id
    invoice_reference_nr
    total_paid
    signed_at
    requested_amount
    schedule_type
    unpaid_principal
    paid_principal
    next_payment_amount
    next_installment_date
    free_hp_enabled
    can_manually_convert_to_credit_account
    credit_info {
      monthly_payment
    }
    merchant {
      logo_path
      name
      campaign {
        converting_schedule_name
      }
    }
  }
}

query ApplicationRemainingAmount($applicationId: Int!) {
  application_remaining_amount(application_id: $applicationId)
}
