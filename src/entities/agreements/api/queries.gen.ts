/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../shared/types/api.gen';

import {
  useQuery,
  useSuspenseQuery,
  UseQueryOptions,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';
import { fetcher } from '@lib/fetcher';
export type UserAgreementFragment = {
  id: number;
  schedule_type: Types.ApplicationScheduleType;
  requested_amount: number;
  reference_key: string;
  status: Types.ApplicationStatus;
  merchant?: {
    id: number;
    name: string;
    logo_path?: string | null;
    campaign?: { converting_schedule_name?: string | null } | null;
  } | null;
};

export type UserAgreementsQueryVariables = Types.Exact<{
  userId: Types.Scalars['Int']['input'];
  limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  statuses?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.AdminApplicationStatuses>>
    | Types.InputMaybe<Types.AdminApplicationStatuses>
  >;
}>;

export type UserAgreementsQuery = {
  user_applications?: {
    last_page: number;
    total: number;
    data?: Array<{
      id: number;
      schedule_type: Types.ApplicationScheduleType;
      requested_amount: number;
      reference_key: string;
      status: Types.ApplicationStatus;
      merchant?: {
        id: number;
        name: string;
        logo_path?: string | null;
        campaign?: { converting_schedule_name?: string | null } | null;
      } | null;
    } | null> | null;
  } | null;
};

export const UserAgreementFragmentDoc = `
    fragment UserAgreement on UserApplicationIndex {
  id
  schedule_type
  merchant {
    id
    name
    logo_path
    campaign {
      converting_schedule_name
    }
  }
  requested_amount
  reference_key
  status
}
    `;
export const UserAgreementsDocument = `
    query UserAgreements($userId: Int!, $limit: Int, $statuses: [admin_application_statuses]) {
  user_applications(user_id: $userId, limit: $limit, statuses: $statuses) {
    data {
      ...UserAgreement
    }
    last_page
    total
  }
}
    ${UserAgreementFragmentDoc}`;

export const useUserAgreementsQuery = <
  TData = UserAgreementsQuery,
  TError = unknown,
>(
  variables: UserAgreementsQueryVariables,
  options?: Omit<
    UseQueryOptions<UserAgreementsQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseQueryOptions<UserAgreementsQuery, TError, TData>['queryKey'];
  },
) => {
  return useQuery<UserAgreementsQuery, TError, TData>({
    queryKey: ['UserAgreements', variables],
    queryFn: fetcher<UserAgreementsQuery, UserAgreementsQueryVariables>(
      UserAgreementsDocument,
      variables,
    ),
    ...options,
  });
};

useUserAgreementsQuery.getKey = (variables: UserAgreementsQueryVariables) => [
  'UserAgreements',
  variables,
];

export const useSuspenseUserAgreementsQuery = <
  TData = UserAgreementsQuery,
  TError = unknown,
>(
  variables: UserAgreementsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<UserAgreementsQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseSuspenseQueryOptions<
      UserAgreementsQuery,
      TError,
      TData
    >['queryKey'];
  },
) => {
  return useSuspenseQuery<UserAgreementsQuery, TError, TData>({
    queryKey: ['UserAgreementsSuspense', variables],
    queryFn: fetcher<UserAgreementsQuery, UserAgreementsQueryVariables>(
      UserAgreementsDocument,
      variables,
    ),
    ...options,
  });
};

useSuspenseUserAgreementsQuery.getKey = (
  variables: UserAgreementsQueryVariables,
) => ['UserAgreementsSuspense', variables];

useUserAgreementsQuery.fetcher = (
  variables: UserAgreementsQueryVariables,
  options?: RequestInit['headers'],
) =>
  fetcher<UserAgreementsQuery, UserAgreementsQueryVariables>(
    UserAgreementsDocument,
    variables,
    options,
  );
