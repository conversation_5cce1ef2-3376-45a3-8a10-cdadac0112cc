fragment UserAgreement on UserApplicationIndex {
  id
  schedule_type
  merchant {
    id
    name
    logo_path
    campaign {
      converting_schedule_name
    }
  }
  requested_amount
  reference_key
  status
}

query UserAgreements(
  $userId: Int!
  $limit: Int
  $statuses: [admin_application_statuses]
) {
  user_applications(user_id: $userId, limit: $limit, statuses: $statuses) {
    data {
      ...UserAgreement
    }
    last_page
    total
  }
}
