import { useMemo } from 'react';

type UseCreditLineMonthlyPaymentParams = {
  annualPctRate: number;
  maxPeriodMonths: number;
  unpaidPrincipal: number;
  newAmount?: number;
};

export const useCreditLineMonthlyPayment = ({
  annualPctRate,
  maxPeriodMonths,
  unpaidPrincipal,
  newAmount = 0,
}: UseCreditLineMonthlyPaymentParams) =>
  useMemo(() => {
    const principal = unpaidPrincipal + newAmount;
    const interestPerMonth = (principal * (annualPctRate / 100)) / 12;

    return Math.max(
      interestPerMonth + principal / maxPeriodMonths,
      Math.min(interestPerMonth + principal, 10),
    );
  }, [annualPctRate, maxPeriodMonths, newAmount, unpaidPrincipal]);
