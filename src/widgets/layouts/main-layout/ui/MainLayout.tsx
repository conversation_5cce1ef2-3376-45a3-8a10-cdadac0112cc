import {
  UserInvoiceState,
  useUserInvoiceNotification,
} from '@entities/invoices';
import { Sidebar } from '@entities/sidebar';
import { LogoutButton } from '@features/auth/logout/ui/LogoutButton';
import { useIsMobileView } from '@hooks/system';
import { useMainLayout } from '@widgets/layouts/main-layout/hooks';
import { lazy, type PropsWithChildren } from 'react';

import { cn } from '@/shared/utils/tailwind';

import { MainLayoutFooter } from './MainLayoutFooter';
import { MainLayoutHeader } from './MainLayoutHeader';

const MainLayoutDesktopLanguageSelector = lazy(() =>
  import('./MainLayoutDesktopLanguageSelector').then(
    ({ MainLayoutDesktopLanguageSelector }) => ({
      default: MainLayoutDesktopLanguageSelector,
    }),
  ),
);
const MainLayoutMobileLanguageSelector = lazy(() =>
  import('./MainLayoutMobileLanguageSelector').then(
    ({ MainLayoutMobileLanguageSelector }) => ({
      default: MainLayoutMobileLanguageSelector,
    }),
  ),
);

const UserInvoiceNotification = lazy(() =>
  import('@entities/invoices').then(({ UserInvoiceNotification }) => ({
    default: UserInvoiceNotification,
  })),
);

export const MainLayout = ({ children }: PropsWithChildren) => {
  const isMobileView = useIsMobileView();
  const { sidebarNavLinks } = useMainLayout();
  const { data: userInvoice } = useUserInvoiceNotification();

  const hasUserInvoice = !!userInvoice?.state;
  const isUserInvoiceOverdue = userInvoice?.state === UserInvoiceState.OVERDUE;

  return (
    <div className="grid-areas-[header,invoice,content] md:grid-areas-[invoice_invoice,sidebar_content] grid h-full grid-rows-[auto,auto,1fr] md:grid-cols-[auto,1fr] md:grid-rows-[auto,1fr]">
      <MainLayoutHeader className="sticky top-0 md:hidden" />
      <div
        className={cn(
          'grid-in-[invoice]',
          isUserInvoiceOverdue && 'sticky top-[65px] z-20 md:top-0',
        )}
      >
        {hasUserInvoice ? <UserInvoiceNotification /> : null}
      </div>
      <Sidebar
        after={<LogoutButton />}
        className="grid-in-[sidebar]"
        navItems={sidebarNavLinks}
        before={
          <div className="pt-14 md:pt-8">
            {isMobileView ? (
              <MainLayoutMobileLanguageSelector />
            ) : (
              <MainLayoutDesktopLanguageSelector />
            )}
          </div>
        }
      />
      <div className="grid-in-[content] no-scrollbar size-full overflow-auto">
        <div className="mx-auto flex size-full max-w-[61.25rem] flex-col">
          <main className="flex-1">{children}</main>
          <MainLayoutFooter />
        </div>
      </div>
    </div>
  );
};
