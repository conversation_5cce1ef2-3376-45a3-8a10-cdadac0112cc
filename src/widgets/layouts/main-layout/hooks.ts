import { LOCIZE_NAMESPACES, LOCIZE_SIDEBAR_KEYS } from '@config/locize';
import { OLD_APP_ROUTE_NAME, ROUTE_NAMES } from '@config/routes';
import { useAppConfig, useFeatureToggles } from '@hooks/system';
import AgreementsIcon from '@icons/business-handshake.svg?react';
import SupportIcon from '@icons/chat.svg?react';
import PremiumIcon from '@icons/crown.svg?react';
import DashboardIcon from '@icons/four-circles.svg?react';
import InvoicesIcon from '@icons/invoice-receipt.svg?react';
import CreditLineIcon from '@icons/products/credit-line.svg?react';
import ProfileIcon from '@icons/profile.svg?react';
import InsuranceIcon from '@icons/shield.svg?react';
import { useTranslation } from 'react-i18next';

export const useMainLayout = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.sidebar);
  const { supportUrl } = useAppConfig();
  const featureToggles = useFeatureToggles();

  const sidebarNavLinks = [
    {
      icon: DashboardIcon,
      title: t(LOCIZE_SIDEBAR_KEYS.dashboardNavItem),
      href: ROUTE_NAMES.dashboard,
    },
    featureToggles.invoicesFeature
      ? {
          href: ROUTE_NAMES.invoices,
          title: t(LOCIZE_SIDEBAR_KEYS.invoicesNavItem),
          icon: InvoicesIcon,
        }
      : {
          isExternal: true,
          href: OLD_APP_ROUTE_NAME.invoices,
          title: t(LOCIZE_SIDEBAR_KEYS.invoicesNavItem),
          icon: InvoicesIcon,
        },
    {
      href: ROUTE_NAMES.creditLine,
      title: t(LOCIZE_SIDEBAR_KEYS.creditLineNavItem),
      icon: CreditLineIcon,
    },
    featureToggles.agreementsFeature
      ? {
          href: ROUTE_NAMES.agreements,
          title: t(LOCIZE_SIDEBAR_KEYS.agreementsNavItem),
          icon: AgreementsIcon,
        }
      : {
          isExternal: true,
          href: OLD_APP_ROUTE_NAME.contracts,
          title: t(LOCIZE_SIDEBAR_KEYS.agreementsNavItem),
          icon: AgreementsIcon,
        },
    {
      href: ROUTE_NAMES.premium,
      title: t(LOCIZE_SIDEBAR_KEYS.premiumNavItem),
      icon: PremiumIcon,
    },
    ...(featureToggles.insuranceFeature
      ? [
          {
            href: ROUTE_NAMES.insurance,
            title: t(LOCIZE_SIDEBAR_KEYS.insuranceNavItem),
            icon: InsuranceIcon,
          },
        ]
      : []),
    {
      isExternal: true,
      isOpenInNewTab: true,
      href: supportUrl,
      title: t(LOCIZE_SIDEBAR_KEYS.supportNavItem),
      icon: SupportIcon,
    },
    featureToggles.profileFeature
      ? {
          href: ROUTE_NAMES.profile,
          title: t(LOCIZE_SIDEBAR_KEYS.profileNavItem),
          icon: ProfileIcon,
        }
      : {
          isExternal: true,
          href: OLD_APP_ROUTE_NAME.profile,
          title: t(LOCIZE_SIDEBAR_KEYS.profileNavItem),
          icon: ProfileIcon,
        },
  ];

  return {
    sidebarNavLinks,
  };
};
