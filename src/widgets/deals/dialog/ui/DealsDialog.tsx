import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { DialogContent } from '@components/ui/dialog';
import { APP_COUNTRY } from '@config/envs';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useRudderStackAnalytics } from '@entities/analytics';
import type { DealId } from '@entities/deals';
import { useGetDeal } from '@entities/deals/hooks';
import { useToast } from '@hooks/system';
import { DEALS_DIALOG_RUDDERSTACK_EVENTS } from '@widgets/deals/dialog/config';
import { type FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useCopyToClipboard } from 'react-use';

import styles from './DealsDialog.module.css';

type DealsDialogProps = {
  dealId: DealId;
};

export const DealsDialog: FC<DealsDialogProps> = ({ dealId }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const getDeal = useGetDeal();
  const {
    brand,
    discountCode,
    title,
    websiteUrl,
    discount,
    imageSrc,
    description,
    discountDescription,
  } = getDeal(dealId);
  const rudderStackAnalytics = useRudderStackAnalytics();
  const [{ error }, copyToClipboard] = useCopyToClipboard();
  const [isCopied, setIsCopied] = useState(false);
  const { showErrorMessage } = useToast();

  const handlePromoCodeCopyButtonClick = () => {
    if (isCopied || !discountCode) return;

    copyToClipboard(discountCode);
    setIsCopied(true);
    rudderStackAnalytics.trackEvent({
      event:
        DEALS_DIALOG_RUDDERSTACK_EVENTS.CUSTOMER_PROFILE_ESTO_DEAL_PROMOCODE_COPIED,
      properties: {
        country: APP_COUNTRY,
        brandId: dealId,
        brand: brand,
        code: discountCode,
      },
    });

    setTimeout(() => {
      setIsCopied(false);
    }, 1300);
  };

  const handleRedirectButtonClick = () => {
    rudderStackAnalytics.trackEvent({
      event:
        DEALS_DIALOG_RUDDERSTACK_EVENTS.CUSTOMER_PROFILE_ESTO_DEAL_LINK_CLICKED,
      properties: {
        country: APP_COUNTRY,
        brandId: dealId,
        brand: brand,
        to_url: websiteUrl.toString(),
      },
    });
  };

  useEffect(() => {
    if (error) showErrorMessage(error.message);
  }, [error, showErrorMessage]);

  return (
    <DialogContent className={styles.wrapper}>
      <div className={styles.container}>
        <img
          src="/images/dashboard/deal-badge.svg"
          alt={brand}
          className={styles.badge}
        />
        <div className={styles.image}>
          <img src={imageSrc} alt={brand} />
        </div>
        <div className={styles.content}>
          <Typography variant="text-m">{brand}</Typography>
          <Typography variant="xs">{t(title)}</Typography>
          {discountCode ? (
            <Typography
              variant="text-l"
              className={styles.discount}
              affects="semibold"
            >
              {t(description, {
                discount: t(discount),
                discountCode,
              })}
            </Typography>
          ) : null}
        </div>
        <div className={styles.footer}>
          {discountDescription ? (
            <Typography variant="text-s" className={styles.footerSmallText}>
              {t(discountDescription)}
            </Typography>
          ) : null}
          {discountCode ? (
            <>
              <Typography variant="text-s" className={styles.footerText}>
                {t(LOCIZE_DEALS_KEYS.dialogCopyDisclaimer)}
              </Typography>
              <Button variant="grey" onClick={handlePromoCodeCopyButtonClick}>
                {isCopied
                  ? t(LOCIZE_DEALS_KEYS.dialogCopyButtonSuccess)
                  : t(LOCIZE_DEALS_KEYS.dialogCopyButton)}
              </Button>
            </>
          ) : null}
          <Button onClick={handleRedirectButtonClick} asChild>
            <a href={websiteUrl.toString()} target="_blank" rel="noreferrer">
              {t(LOCIZE_DEALS_KEYS.dialogRedirectButton)}
            </a>
          </Button>
        </div>
      </div>
    </DialogContent>
  );
};
