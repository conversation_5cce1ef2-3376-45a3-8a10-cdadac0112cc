import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
} from '@components/ui/carousel';
import { APP_COUNTRY } from '@config/envs';
import { ROUTE_NAMES } from '@config/routes';
import { useRudderStackAnalytics } from '@entities/analytics';
import { DealCard, type DealId } from '@entities/deals';
import { useGetDeal } from '@entities/deals/hooks';
import { Link } from '@tanstack/react-router';
import AutoScroll from 'embla-carousel-auto-scroll';
import { useState } from 'react';

import { DEALS_IDS, RUDDERSTACK_DEALS_EVENTS } from '../config';
import styles from './DealsList.module.css';

export const DealsList = () => {
  const [api, setApi] = useState<CarouselApi>();
  const rudderStackAnalytics = useRudderStackAnalytics();
  const getDeal = useGetDeal();

  const handleCardClick = (dealId: DealId) => () => {
    const { brand } = getDeal(dealId);
    rudderStackAnalytics.trackEvent({
      event: RUDDERSTACK_DEALS_EVENTS.CUSTOMER_PROFILE_ESTO_DEAL_CLICKED,
      properties: {
        country: APP_COUNTRY,
        brandId: dealId,
        brand: brand,
      },
    });
  };

  return (
    <Carousel
      className={styles.container}
      onMouseLeave={() => {
        setTimeout(() => {
          api?.plugins().autoScroll.play();
        }, 1500);
      }}
      opts={{ loop: true, align: 'start', dragFree: true }}
      setApi={setApi}
      plugins={[
        AutoScroll({ playOnInit: true, stopOnMouseEnter: true, speed: 0.5 }),
      ]}
    >
      <CarouselContent isInteractive className="py-5">
        {DEALS_IDS.map((dealId) => (
          <CarouselItem key={dealId} className="basis-[12.25rem] pl-6">
            <Link
              onClick={handleCardClick(dealId)}
              to={ROUTE_NAMES.current}
              search={{ selectedDeal: dealId }}
              resetScroll={false}
            >
              <DealCard dealId={dealId} />
            </Link>
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  );
};
