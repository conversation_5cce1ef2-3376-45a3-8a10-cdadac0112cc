import { Skeleton } from '@components/ui/skeleton';
import { cn } from '@utils/tailwind';
import { type FC, lazy, Suspense } from 'react';

import Logo from '@/shared/assets/logo.svg?react';

const LanguageSelector = lazy(() =>
  import('@features/language-selector').then(({ LanguageSelector }) => ({
    default: LanguageSelector,
  })),
);

type AuthHeaderProps = {
  className?: string;
};

export const AuthHeader: FC<AuthHeaderProps> = ({ className }) => (
  <header
    className={cn(
      'relative flex items-center justify-between gap-4 bg-transparent px-6 py-3 md:bg-primary-white md:px-8',
      className,
    )}
  >
    <Logo />
    <Suspense fallback={<Skeleton className="h-10 w-14" />}>
      <LanguageSelector />
    </Suspense>
  </header>
);
