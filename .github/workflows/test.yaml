name: 'Test'
on:
  workflow_call:
    secrets:
      VERCEL_PROJECT_ID:
        required: true
        description: 'The Vercel project ID to deploy to'
      VERCEL_ORG_ID:
        required: true
        description: 'The Vercel organization ID to deploy to'
      VERCEL_TOKEN:
        required: true
        description: 'The Vercel token to use for deployment'
jobs:
  test:
    env:
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install & Cache dependencies
        uses: ./.github/actions/install-and-cache-deps
      - name: Pull Vercel Environment Information
        run: |
          vercel env pull --environment=preview --git-branch=dekker-dev1 --token=${{ env.VERCEL_TOKEN }}
          mv .env.local .env.test
      - name: Run Tests
        run: pnpm run test
