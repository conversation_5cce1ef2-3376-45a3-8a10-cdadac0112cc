name: Vercel Production deployment
on:
  workflow_call:
    secrets:
      VERCEL_PROJECT_ID:
        required: true
        description: 'The Vercel project ID to deploy to'
      VERCEL_ORG_ID:
        required: true
        description: 'The Vercel organization ID to deploy to'
      VERCEL_TOKEN:
        required: true
        description: 'The Vercel token to use for deployment'

jobs:
  deploy-production:
    env:
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install & Cache dependencies
        uses: ./.github/actions/install-and-cache-deps
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ env.VERCEL_TOKEN }}
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ env.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ env.VERCEL_TOKEN }}
