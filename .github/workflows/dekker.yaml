name: Dekker Deployment
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_EE }}
on:
  push:
    branches:
      - 'dekker-dev0'
      - 'dekker-dev1'
      - 'dekker-dev2'
      - 'dekker-dev3'
jobs:
  deploy-dekker:
    uses: ./.github/workflows/vercel-preview-deploy.yaml
    secrets:
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_EE }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
