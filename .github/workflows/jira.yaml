name: Link PR to JIRA
on:
  pull_request:
    types:
      - opened
    # branches:
    #   - "feature/**"
    #   - "hotfix/**"

jobs:
  jira:
    runs-on: ubuntu-latest
    env:
      JIRA_BASE_URL: ${{ secrets.JIRA_BASE_URL }}
      JIRA_USER_EMAIL: ${{ secrets.JIRA_USER_EMAIL }}
      JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Jira CLI
        uses: atlassian/gajira-cli@master

      - name: Login to JIRA
        uses: atlassian/gajira-login@master

      - name: Find in commit messages
        id: jira_key
        uses: atlassian/gajira-find-issue-key@master
        with:
          string: ${{ github.head_ref }}

      - name: Get task description
        id: issue_details
        run: |
          echo "::set-output name=summary::$(jira view --template=json --gjq="fields.summary" ${{ steps.jira_key.outputs.issue }})"
          echo "::set-output name=description::$(jira view --template=json --expand=renderedFields --gjq="renderedFields.description" ${{ steps.jira_key.outputs.issue }} | tr -d '\n\t')"

      - uses: tzkhan/pr-update-action@v2
        with:
          repo-token: ${{ github.token }}
          head-branch-regex: '.*'
          title-update-action: 'replace'
          title-template: ${{ github.head_ref }}
          body-update-action: 'replace'
          body-template: |
            ## ${{ steps.issue_details.outputs.summary }}
            ### https://estoas.atlassian.net/browse/${{ steps.jira_key.outputs.issue }}
            ${{ steps.issue_details.outputs.description }}
