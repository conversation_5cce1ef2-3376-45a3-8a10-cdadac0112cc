name: Vercel Staging deployment
on:
  workflow_call:
    inputs:
      git-branch-ref-name:
        required: false
        description: 'The name of the branch from which the envs are pulled'
        type: string
        default: ${{ github.ref_name }}

    secrets:
      VERCEL_PROJECT_ID:
        required: true
        description: 'The Vercel project ID to deploy to'
      VERCEL_ORG_ID:
        required: true
        description: 'The Vercel organization ID to deploy to'
      VERCEL_TOKEN:
        required: true
        description: 'The Vercel token to use for deployment'

jobs:
  deploy-preview:
    env:
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install & Cache dependencies
        uses: ./.github/actions/install-and-cache-deps
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --git-branch=${{ inputs.git-branch-ref-name }} --token=${{ env.VERCEL_TOKEN }}
      - name: Build Project Artifacts
        run: vercel build --token=${{ env.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --token=${{ env.VERCEL_TOKEN }}
