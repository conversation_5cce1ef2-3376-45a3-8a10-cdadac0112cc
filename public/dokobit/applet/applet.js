!(function (t, e) {
  'function' == typeof define && define.amd
    ? define(e)
    : 'object' == typeof exports
      ? (module.exports = e(require, exports, module))
      : (t.Tether = e());
})(this, function (t, e, o) {
  'use strict';
  function i(t, e) {
    if (!(t instanceof e))
      throw new TypeError('Cannot call a class as a function');
  }
  function n(t) {
    var e = getComputedStyle(t),
      o = e.position;
    if ('fixed' === o) return t;
    for (var i = t; (i = i.parentNode); ) {
      var n = void 0;
      try {
        n = getComputedStyle(i);
      } catch (r) {}
      if ('undefined' == typeof n || null === n) return i;
      var s = n,
        a = s.overflow,
        f = s.overflowX,
        h = s.overflowY;
      if (
        /(auto|scroll)/.test(a + h + f) &&
        ('absolute' !== o ||
          ['relative', 'absolute', 'fixed'].indexOf(n.position) >= 0)
      )
        return i;
    }
    return document.body;
  }
  function r(t) {
    var e = void 0;
    t === document
      ? ((e = document), (t = document.documentElement))
      : (e = t.ownerDocument);
    var o = e.documentElement,
      i = {},
      n = t.getBoundingClientRect();
    for (var r in n) i[r] = n[r];
    var s = x(e);
    return (
      (i.top -= s.top),
      (i.left -= s.left),
      'undefined' == typeof i.width &&
        (i.width = document.body.scrollWidth - i.left - i.right),
      'undefined' == typeof i.height &&
        (i.height = document.body.scrollHeight - i.top - i.bottom),
      (i.top = i.top - o.clientTop),
      (i.left = i.left - o.clientLeft),
      (i.right = e.body.clientWidth - i.width - i.left),
      (i.bottom = e.body.clientHeight - i.height - i.top),
      i
    );
  }
  function s(t) {
    return t.offsetParent || document.documentElement;
  }
  function a() {
    var t = document.createElement('div');
    (t.style.width = '100%'), (t.style.height = '200px');
    var e = document.createElement('div');
    f(e.style, {
      position: 'absolute',
      top: 0,
      left: 0,
      pointerEvents: 'none',
      visibility: 'hidden',
      width: '200px',
      height: '150px',
      overflow: 'hidden',
    }),
      e.appendChild(t),
      document.body.appendChild(e);
    var o = t.offsetWidth;
    e.style.overflow = 'scroll';
    var i = t.offsetWidth;
    o === i && (i = e.clientWidth), document.body.removeChild(e);
    var n = o - i;
    return { width: n, height: n };
  }
  function f() {
    var t =
        arguments.length <= 0 || void 0 === arguments[0] ? {} : arguments[0],
      e = [];
    return (
      Array.prototype.push.apply(e, arguments),
      e.slice(1).forEach(function (e) {
        if (e) for (var o in e) ({}).hasOwnProperty.call(e, o) && (t[o] = e[o]);
      }),
      t
    );
  }
  function h(t, e) {
    if ('undefined' != typeof t.classList)
      e.split(' ').forEach(function (e) {
        e.trim() && t.classList.remove(e);
      });
    else {
      var o = new RegExp('(^| )' + e.split(' ').join('|') + '( |$)', 'gi'),
        i = u(t).replace(o, ' ');
      p(t, i);
    }
  }
  function l(t, e) {
    if ('undefined' != typeof t.classList)
      e.split(' ').forEach(function (e) {
        e.trim() && t.classList.add(e);
      });
    else {
      h(t, e);
      var o = u(t) + (' ' + e);
      p(t, o);
    }
  }
  function d(t, e) {
    if ('undefined' != typeof t.classList) return t.classList.contains(e);
    var o = u(t);
    return new RegExp('(^| )' + e + '( |$)', 'gi').test(o);
  }
  function u(t) {
    return t.className instanceof SVGAnimatedString
      ? t.className.baseVal
      : t.className;
  }
  function p(t, e) {
    t.setAttribute('class', e);
  }
  function c(t, e, o) {
    o.forEach(function (o) {
      -1 === e.indexOf(o) && d(t, o) && h(t, o);
    }),
      e.forEach(function (e) {
        d(t, e) || l(t, e);
      });
  }
  function i(t, e) {
    if (!(t instanceof e))
      throw new TypeError('Cannot call a class as a function');
  }
  function g(t, e) {
    var o = arguments.length <= 2 || void 0 === arguments[2] ? 1 : arguments[2];
    return t + o >= e && e >= t - o;
  }
  function m() {
    return 'undefined' != typeof performance &&
      'undefined' != typeof performance.now
      ? performance.now()
      : +new Date();
  }
  function v() {
    for (
      var t = { top: 0, left: 0 }, e = arguments.length, o = Array(e), i = 0;
      e > i;
      i++
    )
      o[i] = arguments[i];
    return (
      o.forEach(function (e) {
        var o = e.top,
          i = e.left;
        'string' == typeof o && (o = parseFloat(o, 10)),
          'string' == typeof i && (i = parseFloat(i, 10)),
          (t.top += o),
          (t.left += i);
      }),
      t
    );
  }
  function y(t, e) {
    return (
      'string' == typeof t.left &&
        -1 !== t.left.indexOf('%') &&
        (t.left = (parseFloat(t.left, 10) / 100) * e.width),
      'string' == typeof t.top &&
        -1 !== t.top.indexOf('%') &&
        (t.top = (parseFloat(t.top, 10) / 100) * e.height),
      t
    );
  }
  function b(t, e) {
    return (
      'scrollParent' === e
        ? (e = t.scrollParent)
        : 'window' === e &&
          (e = [
            pageXOffset,
            pageYOffset,
            innerWidth + pageXOffset,
            innerHeight + pageYOffset,
          ]),
      e === document && (e = e.documentElement),
      'undefined' != typeof e.nodeType &&
        !(function () {
          var t = r(e),
            o = t,
            i = getComputedStyle(e);
          (e = [o.left, o.top, t.width + o.left, t.height + o.top]),
            U.forEach(function (t, o) {
              (t = t[0].toUpperCase() + t.substr(1)),
                'Top' === t || 'Left' === t
                  ? (e[o] += parseFloat(i['border' + t + 'Width']))
                  : (e[o] -= parseFloat(i['border' + t + 'Width']));
            });
        })(),
      e
    );
  }
  var w = (function () {
      function t(t, e) {
        for (var o = 0; o < e.length; o++) {
          var i = e[o];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            'value' in i && (i.writable = !0),
            Object.defineProperty(t, i.key, i);
        }
      }
      return function (e, o, i) {
        return o && t(e.prototype, o), i && t(e, i), e;
      };
    })(),
    C = void 0;
  'undefined' == typeof C && (C = { modules: [] });
  var O = (function () {
      var t = 0;
      return function () {
        return ++t;
      };
    })(),
    E = {},
    x = function (t) {
      var e = t._tetherZeroElement;
      'undefined' == typeof e &&
        ((e = t.createElement('div')),
        e.setAttribute('data-tether-id', O()),
        f(e.style, { top: 0, left: 0, position: 'absolute' }),
        t.body.appendChild(e),
        (t._tetherZeroElement = e));
      var o = e.getAttribute('data-tether-id');
      if ('undefined' == typeof E[o]) {
        E[o] = {};
        var i = e.getBoundingClientRect();
        for (var n in i) E[o][n] = i[n];
        T(function () {
          delete E[o];
        });
      }
      return E[o];
    },
    A = [],
    T = function (t) {
      A.push(t);
    },
    S = function () {
      for (var t = void 0; (t = A.pop()); ) t();
    },
    W = (function () {
      function t() {
        i(this, t);
      }
      return (
        w(t, [
          {
            key: 'on',
            value: function (t, e, o) {
              var i =
                arguments.length <= 3 || void 0 === arguments[3]
                  ? !1
                  : arguments[3];
              'undefined' == typeof this.bindings && (this.bindings = {}),
                'undefined' == typeof this.bindings[t] &&
                  (this.bindings[t] = []),
                this.bindings[t].push({ handler: e, ctx: o, once: i });
            },
          },
          {
            key: 'once',
            value: function (t, e, o) {
              this.on(t, e, o, !0);
            },
          },
          {
            key: 'off',
            value: function (t, e) {
              if (
                'undefined' == typeof this.bindings ||
                'undefined' == typeof this.bindings[t]
              )
                if ('undefined' == typeof e) delete this.bindings[t];
                else
                  for (var o = 0; o < this.bindings[t].length; )
                    this.bindings[t][o].handler === e
                      ? this.bindings[t].splice(o, 1)
                      : ++o;
            },
          },
          {
            key: 'trigger',
            value: function (t) {
              if ('undefined' != typeof this.bindings && this.bindings[t]) {
                for (
                  var e = 0,
                    o = arguments.length,
                    i = Array(o > 1 ? o - 1 : 0),
                    n = 1;
                  o > n;
                  n++
                )
                  i[n - 1] = arguments[n];
                for (; e < this.bindings[t].length; ) {
                  var r = this.bindings[t][e],
                    s = r.handler,
                    a = r.ctx,
                    f = r.once,
                    h = a;
                  'undefined' == typeof h && (h = this),
                    s.apply(h, i),
                    f ? this.bindings[t].splice(e, 1) : ++e;
                }
              }
            },
          },
        ]),
        t
      );
    })();
  C.Utils = {
    getScrollParent: n,
    getBounds: r,
    getOffsetParent: s,
    extend: f,
    addClass: l,
    removeClass: h,
    hasClass: d,
    updateClasses: c,
    defer: T,
    flush: S,
    uniqueId: O,
    Evented: W,
    getScrollBarSize: a,
  };
  var M = (function () {
      function t(t, e) {
        var o = [],
          i = !0,
          n = !1,
          r = void 0;
        try {
          for (
            var s, a = t[Symbol.iterator]();
            !(i = (s = a.next()).done) &&
            (o.push(s.value), !e || o.length !== e);
            i = !0
          );
        } catch (f) {
          (n = !0), (r = f);
        } finally {
          try {
            !i && a['return'] && a['return']();
          } finally {
            if (n) throw r;
          }
        }
        return o;
      }
      return function (e, o) {
        if (Array.isArray(e)) return e;
        if (Symbol.iterator in Object(e)) return t(e, o);
        throw new TypeError(
          'Invalid attempt to destructure non-iterable instance',
        );
      };
    })(),
    w = (function () {
      function t(t, e) {
        for (var o = 0; o < e.length; o++) {
          var i = e[o];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            'value' in i && (i.writable = !0),
            Object.defineProperty(t, i.key, i);
        }
      }
      return function (e, o, i) {
        return o && t(e.prototype, o), i && t(e, i), e;
      };
    })();
  if ('undefined' == typeof C)
    throw new Error('You must include the utils.js file before tether.js');
  var P = C.Utils,
    n = P.getScrollParent,
    r = P.getBounds,
    s = P.getOffsetParent,
    f = P.extend,
    l = P.addClass,
    h = P.removeClass,
    c = P.updateClasses,
    T = P.defer,
    S = P.flush,
    a = P.getScrollBarSize,
    k = (function () {
      if ('undefined' == typeof document) return '';
      for (
        var t = document.createElement('div'),
          e = [
            'transform',
            'webkitTransform',
            'OTransform',
            'MozTransform',
            'msTransform',
          ],
          o = 0;
        o < e.length;
        ++o
      ) {
        var i = e[o];
        if (void 0 !== t.style[i]) return i;
      }
    })(),
    B = [],
    _ = function () {
      B.forEach(function (t) {
        t.position(!1);
      }),
        S();
    };
  !(function () {
    var t = null,
      e = null,
      o = null,
      i = function n() {
        return 'undefined' != typeof e && e > 16
          ? ((e = Math.min(e - 16, 250)), void (o = setTimeout(n, 250)))
          : void (
              ('undefined' != typeof t && m() - t < 10) ||
              ('undefined' != typeof o && (clearTimeout(o), (o = null)),
              (t = m()),
              _(),
              (e = m() - t))
            );
      };
    'undefined' != typeof window &&
      ['resize', 'scroll', 'touchmove'].forEach(function (t) {
        window.addEventListener(t, i);
      });
  })();
  var z = { center: 'center', left: 'right', right: 'left' },
    F = { middle: 'middle', top: 'bottom', bottom: 'top' },
    L = {
      top: 0,
      left: 0,
      middle: '50%',
      center: '50%',
      bottom: '100%',
      right: '100%',
    },
    Y = function (t, e) {
      var o = t.left,
        i = t.top;
      return (
        'auto' === o && (o = z[e.left]),
        'auto' === i && (i = F[e.top]),
        { left: o, top: i }
      );
    },
    H = function (t) {
      var e = t.left,
        o = t.top;
      return (
        'undefined' != typeof L[t.left] && (e = L[t.left]),
        'undefined' != typeof L[t.top] && (o = L[t.top]),
        { left: e, top: o }
      );
    },
    X = function (t) {
      var e = t.split(' '),
        o = M(e, 2),
        i = o[0],
        n = o[1];
      return { top: i, left: n };
    },
    j = X,
    N = (function () {
      function t(e) {
        var o = this;
        i(this, t),
          (this.position = this.position.bind(this)),
          B.push(this),
          (this.history = []),
          this.setOptions(e, !1),
          C.modules.forEach(function (t) {
            'undefined' != typeof t.initialize && t.initialize.call(o);
          }),
          this.position();
      }
      return (
        w(t, [
          {
            key: 'getClass',
            value: function () {
              var t =
                  arguments.length <= 0 || void 0 === arguments[0]
                    ? ''
                    : arguments[0],
                e = this.options.classes;
              return 'undefined' != typeof e && e[t]
                ? this.options.classes[t]
                : this.options.classPrefix
                  ? this.options.classPrefix + '-' + t
                  : t;
            },
          },
          {
            key: 'setOptions',
            value: function (t) {
              var e = this,
                o =
                  arguments.length <= 1 || void 0 === arguments[1]
                    ? !0
                    : arguments[1],
                i = {
                  offset: '0 0',
                  targetOffset: '0 0',
                  targetAttachment: 'auto auto',
                  classPrefix: 'tether',
                };
              this.options = f(i, t);
              var r = this.options,
                s = r.element,
                a = r.target,
                h = r.targetModifier;
              if (
                ((this.element = s),
                (this.target = a),
                (this.targetModifier = h),
                'viewport' === this.target
                  ? ((this.target = document.body),
                    (this.targetModifier = 'visible'))
                  : 'scroll-handle' === this.target &&
                    ((this.target = document.body),
                    (this.targetModifier = 'scroll-handle')),
                ['element', 'target'].forEach(function (t) {
                  if ('undefined' == typeof e[t])
                    throw new Error(
                      'Tether Error: Both element and target must be defined',
                    );
                  'undefined' != typeof e[t].jquery
                    ? (e[t] = e[t][0])
                    : 'string' == typeof e[t] &&
                      (e[t] = document.querySelector(e[t]));
                }),
                l(this.element, this.getClass('element')),
                this.options.addTargetClasses !== !1 &&
                  l(this.target, this.getClass('target')),
                !this.options.attachment)
              )
                throw new Error('Tether Error: You must provide an attachment');
              (this.targetAttachment = j(this.options.targetAttachment)),
                (this.attachment = j(this.options.attachment)),
                (this.offset = X(this.options.offset)),
                (this.targetOffset = X(this.options.targetOffset)),
                'undefined' != typeof this.scrollParent && this.disable(),
                'scroll-handle' === this.targetModifier
                  ? (this.scrollParent = this.target)
                  : (this.scrollParent = n(this.target)),
                this.options.enabled !== !1 && this.enable(o);
            },
          },
          {
            key: 'getTargetBounds',
            value: function () {
              if ('undefined' == typeof this.targetModifier)
                return r(this.target);
              if ('visible' === this.targetModifier) {
                if (this.target === document.body)
                  return {
                    top: pageYOffset,
                    left: pageXOffset,
                    height: innerHeight,
                    width: innerWidth,
                  };
                var t = r(this.target),
                  e = {
                    height: t.height,
                    width: t.width,
                    top: t.top,
                    left: t.left,
                  };
                return (
                  (e.height = Math.min(
                    e.height,
                    t.height - (pageYOffset - t.top),
                  )),
                  (e.height = Math.min(
                    e.height,
                    t.height - (t.top + t.height - (pageYOffset + innerHeight)),
                  )),
                  (e.height = Math.min(innerHeight, e.height)),
                  (e.height -= 2),
                  (e.width = Math.min(
                    e.width,
                    t.width - (pageXOffset - t.left),
                  )),
                  (e.width = Math.min(
                    e.width,
                    t.width - (t.left + t.width - (pageXOffset + innerWidth)),
                  )),
                  (e.width = Math.min(innerWidth, e.width)),
                  (e.width -= 2),
                  e.top < pageYOffset && (e.top = pageYOffset),
                  e.left < pageXOffset && (e.left = pageXOffset),
                  e
                );
              }
              if ('scroll-handle' === this.targetModifier) {
                var t = void 0,
                  o = this.target;
                o === document.body
                  ? ((o = document.documentElement),
                    (t = {
                      left: pageXOffset,
                      top: pageYOffset,
                      height: innerHeight,
                      width: innerWidth,
                    }))
                  : (t = r(o));
                var i = getComputedStyle(o),
                  n =
                    o.scrollWidth > o.clientWidth ||
                    [i.overflow, i.overflowX].indexOf('scroll') >= 0 ||
                    this.target !== document.body,
                  s = 0;
                n && (s = 15);
                var a =
                    t.height -
                    parseFloat(i.borderTopWidth) -
                    parseFloat(i.borderBottomWidth) -
                    s,
                  e = {
                    width: 15,
                    height: 0.975 * a * (a / o.scrollHeight),
                    left: t.left + t.width - parseFloat(i.borderLeftWidth) - 15,
                  },
                  f = 0;
                408 > a &&
                  this.target === document.body &&
                  (f = -11e-5 * Math.pow(a, 2) - 0.00727 * a + 22.58),
                  this.target !== document.body &&
                    (e.height = Math.max(e.height, 24));
                var h = this.target.scrollTop / (o.scrollHeight - a);
                return (
                  (e.top =
                    h * (a - e.height - f) +
                    t.top +
                    parseFloat(i.borderTopWidth)),
                  this.target === document.body &&
                    (e.height = Math.max(e.height, 24)),
                  e
                );
              }
            },
          },
          {
            key: 'clearCache',
            value: function () {
              this._cache = {};
            },
          },
          {
            key: 'cache',
            value: function (t, e) {
              return (
                'undefined' == typeof this._cache && (this._cache = {}),
                'undefined' == typeof this._cache[t] &&
                  (this._cache[t] = e.call(this)),
                this._cache[t]
              );
            },
          },
          {
            key: 'enable',
            value: function () {
              var t =
                arguments.length <= 0 || void 0 === arguments[0]
                  ? !0
                  : arguments[0];
              this.options.addTargetClasses !== !1 &&
                l(this.target, this.getClass('enabled')),
                l(this.element, this.getClass('enabled')),
                (this.enabled = !0),
                this.scrollParent !== document &&
                  this.scrollParent.addEventListener('scroll', this.position),
                t && this.position();
            },
          },
          {
            key: 'disable',
            value: function () {
              h(this.target, this.getClass('enabled')),
                h(this.element, this.getClass('enabled')),
                (this.enabled = !1),
                'undefined' != typeof this.scrollParent &&
                  this.scrollParent.removeEventListener(
                    'scroll',
                    this.position,
                  );
            },
          },
          {
            key: 'destroy',
            value: function () {
              var t = this;
              this.disable(),
                B.forEach(function (e, o) {
                  return e === t ? void B.splice(o, 1) : void 0;
                });
            },
          },
          {
            key: 'updateAttachClasses',
            value: function (t, e) {
              var o = this;
              (t = t || this.attachment), (e = e || this.targetAttachment);
              var i = ['left', 'top', 'bottom', 'right', 'middle', 'center'];
              'undefined' != typeof this._addAttachClasses &&
                this._addAttachClasses.length &&
                this._addAttachClasses.splice(0, this._addAttachClasses.length),
                'undefined' == typeof this._addAttachClasses &&
                  (this._addAttachClasses = []);
              var n = this._addAttachClasses;
              t.top && n.push(this.getClass('element-attached') + '-' + t.top),
                t.left &&
                  n.push(this.getClass('element-attached') + '-' + t.left),
                e.top && n.push(this.getClass('target-attached') + '-' + e.top),
                e.left &&
                  n.push(this.getClass('target-attached') + '-' + e.left);
              var r = [];
              i.forEach(function (t) {
                r.push(o.getClass('element-attached') + '-' + t),
                  r.push(o.getClass('target-attached') + '-' + t);
              }),
                T(function () {
                  'undefined' != typeof o._addAttachClasses &&
                    (c(o.element, o._addAttachClasses, r),
                    o.options.addTargetClasses !== !1 &&
                      c(o.target, o._addAttachClasses, r),
                    delete o._addAttachClasses);
                });
            },
          },
          {
            key: 'position',
            value: function () {
              var t = this,
                e =
                  arguments.length <= 0 || void 0 === arguments[0]
                    ? !0
                    : arguments[0];
              if (this.enabled) {
                this.clearCache();
                var o = Y(this.targetAttachment, this.attachment);
                this.updateAttachClasses(this.attachment, o);
                var i = this.cache('element-bounds', function () {
                    return r(t.element);
                  }),
                  n = i.width,
                  f = i.height;
                if (0 === n && 0 === f && 'undefined' != typeof this.lastSize) {
                  var h = this.lastSize;
                  (n = h.width), (f = h.height);
                } else this.lastSize = { width: n, height: f };
                var l = this.cache('target-bounds', function () {
                    return t.getTargetBounds();
                  }),
                  d = l,
                  u = y(H(this.attachment), { width: n, height: f }),
                  p = y(H(o), d),
                  c = y(this.offset, { width: n, height: f }),
                  g = y(this.targetOffset, d);
                (u = v(u, c)), (p = v(p, g));
                for (
                  var m = l.left + p.left - u.left,
                    b = l.top + p.top - u.top,
                    w = 0;
                  w < C.modules.length;
                  ++w
                ) {
                  var O = C.modules[w],
                    E = O.position.call(this, {
                      left: m,
                      top: b,
                      targetAttachment: o,
                      targetPos: l,
                      elementPos: i,
                      offset: u,
                      targetOffset: p,
                      manualOffset: c,
                      manualTargetOffset: g,
                      scrollbarSize: A,
                      attachment: this.attachment,
                    });
                  if (E === !1) return !1;
                  'undefined' != typeof E &&
                    'object' == typeof E &&
                    ((b = E.top), (m = E.left));
                }
                var x = {
                    page: { top: b, left: m },
                    viewport: {
                      top: b - pageYOffset,
                      bottom: pageYOffset - b - f + innerHeight,
                      left: m - pageXOffset,
                      right: pageXOffset - m - n + innerWidth,
                    },
                  },
                  A = void 0;
                return (
                  document.body.scrollWidth > window.innerWidth &&
                    ((A = this.cache('scrollbar-size', a)),
                    (x.viewport.bottom -= A.height)),
                  document.body.scrollHeight > window.innerHeight &&
                    ((A = this.cache('scrollbar-size', a)),
                    (x.viewport.right -= A.width)),
                  (-1 ===
                    ['', 'static'].indexOf(document.body.style.position) ||
                    -1 ===
                      ['', 'static'].indexOf(
                        document.body.parentElement.style.position,
                      )) &&
                    ((x.page.bottom = document.body.scrollHeight - b - f),
                    (x.page.right = document.body.scrollWidth - m - n)),
                  'undefined' != typeof this.options.optimizations &&
                    this.options.optimizations.moveElement !== !1 &&
                    'undefined' == typeof this.targetModifier &&
                    !(function () {
                      var e = t.cache('target-offsetparent', function () {
                          return s(t.target);
                        }),
                        o = t.cache('target-offsetparent-bounds', function () {
                          return r(e);
                        }),
                        i = getComputedStyle(e),
                        n = o,
                        a = {};
                      if (
                        (['Top', 'Left', 'Bottom', 'Right'].forEach(
                          function (t) {
                            a[t.toLowerCase()] = parseFloat(
                              i['border' + t + 'Width'],
                            );
                          },
                        ),
                        (o.right =
                          document.body.scrollWidth -
                          o.left -
                          n.width +
                          a.right),
                        (o.bottom =
                          document.body.scrollHeight -
                          o.top -
                          n.height +
                          a.bottom),
                        x.page.top >= o.top + a.top &&
                          x.page.bottom >= o.bottom &&
                          x.page.left >= o.left + a.left &&
                          x.page.right >= o.right)
                      ) {
                        var f = e.scrollTop,
                          h = e.scrollLeft;
                        x.offset = {
                          top: x.page.top - o.top + f - a.top,
                          left: x.page.left - o.left + h - a.left,
                        };
                      }
                    })(),
                  this.move(x),
                  this.history.unshift(x),
                  this.history.length > 3 && this.history.pop(),
                  e && S(),
                  !0
                );
              }
            },
          },
          {
            key: 'move',
            value: function (t) {
              var e = this;
              if ('undefined' != typeof this.element.parentNode) {
                var o = {};
                for (var i in t) {
                  o[i] = {};
                  for (var n in t[i]) {
                    for (var r = !1, a = 0; a < this.history.length; ++a) {
                      var h = this.history[a];
                      if ('undefined' != typeof h[i] && !g(h[i][n], t[i][n])) {
                        r = !0;
                        break;
                      }
                    }
                    r || (o[i][n] = !0);
                  }
                }
                var l = { top: '', left: '', right: '', bottom: '' },
                  d = function (t, o) {
                    var i = 'undefined' != typeof e.options.optimizations,
                      n = i ? e.options.optimizations.gpu : null;
                    if (n !== !1) {
                      var r = void 0,
                        s = void 0;
                      t.top
                        ? ((l.top = 0), (r = o.top))
                        : ((l.bottom = 0), (r = -o.bottom)),
                        t.left
                          ? ((l.left = 0), (s = o.left))
                          : ((l.right = 0), (s = -o.right)),
                        (l[k] =
                          'translateX(' +
                          Math.round(s) +
                          'px) translateY(' +
                          Math.round(r) +
                          'px)'),
                        'msTransform' !== k && (l[k] += ' translateZ(0)');
                    } else
                      t.top
                        ? (l.top = o.top + 'px')
                        : (l.bottom = o.bottom + 'px'),
                        t.left
                          ? (l.left = o.left + 'px')
                          : (l.right = o.right + 'px');
                  },
                  u = !1;
                if (
                  ((o.page.top || o.page.bottom) &&
                  (o.page.left || o.page.right)
                    ? ((l.position = 'absolute'), d(o.page, t.page))
                    : (o.viewport.top || o.viewport.bottom) &&
                        (o.viewport.left || o.viewport.right)
                      ? ((l.position = 'fixed'), d(o.viewport, t.viewport))
                      : 'undefined' != typeof o.offset &&
                          o.offset.top &&
                          o.offset.left
                        ? !(function () {
                            l.position = 'absolute';
                            var i = e.cache('target-offsetparent', function () {
                              return s(e.target);
                            });
                            s(e.element) !== i &&
                              T(function () {
                                e.element.parentNode.removeChild(e.element),
                                  i.appendChild(e.element);
                              }),
                              d(o.offset, t.offset),
                              (u = !0);
                          })()
                        : ((l.position = 'absolute'),
                          d({ top: !0, left: !0 }, t.page)),
                  !u)
                ) {
                  for (
                    var p = !0, c = this.element.parentNode;
                    c && 'BODY' !== c.tagName;

                  ) {
                    if ('static' !== getComputedStyle(c).position) {
                      p = !1;
                      break;
                    }
                    c = c.parentNode;
                  }
                  p ||
                    (this.element.parentNode.removeChild(this.element),
                    document.body.appendChild(this.element));
                }
                var m = {},
                  v = !1;
                for (var n in l) {
                  var y = l[n],
                    b = this.element.style[n];
                  '' !== b &&
                    '' !== y &&
                    ['top', 'left', 'bottom', 'right'].indexOf(n) >= 0 &&
                    ((b = parseFloat(b)), (y = parseFloat(y))),
                    b !== y && ((v = !0), (m[n] = y));
                }
                v &&
                  T(function () {
                    f(e.element.style, m);
                  });
              }
            },
          },
        ]),
        t
      );
    })();
  (N.modules = []), (C.position = _);
  var R = f(N, C),
    M = (function () {
      function t(t, e) {
        var o = [],
          i = !0,
          n = !1,
          r = void 0;
        try {
          for (
            var s, a = t[Symbol.iterator]();
            !(i = (s = a.next()).done) &&
            (o.push(s.value), !e || o.length !== e);
            i = !0
          );
        } catch (f) {
          (n = !0), (r = f);
        } finally {
          try {
            !i && a['return'] && a['return']();
          } finally {
            if (n) throw r;
          }
        }
        return o;
      }
      return function (e, o) {
        if (Array.isArray(e)) return e;
        if (Symbol.iterator in Object(e)) return t(e, o);
        throw new TypeError(
          'Invalid attempt to destructure non-iterable instance',
        );
      };
    })(),
    P = C.Utils,
    r = P.getBounds,
    f = P.extend,
    c = P.updateClasses,
    T = P.defer,
    U = ['left', 'top', 'right', 'bottom'];
  C.modules.push({
    position: function (t) {
      var e = this,
        o = t.top,
        i = t.left,
        n = t.targetAttachment;
      if (!this.options.constraints) return !0;
      var s = this.cache('element-bounds', function () {
          return r(e.element);
        }),
        a = s.height,
        h = s.width;
      if (0 === h && 0 === a && 'undefined' != typeof this.lastSize) {
        var l = this.lastSize;
        (h = l.width), (a = l.height);
      }
      var d = this.cache('target-bounds', function () {
          return e.getTargetBounds();
        }),
        u = d.height,
        p = d.width,
        g = [this.getClass('pinned'), this.getClass('out-of-bounds')];
      this.options.constraints.forEach(function (t) {
        var e = t.outOfBoundsClass,
          o = t.pinnedClass;
        e && g.push(e), o && g.push(o);
      }),
        g.forEach(function (t) {
          ['left', 'top', 'right', 'bottom'].forEach(function (e) {
            g.push(t + '-' + e);
          });
        });
      var m = [],
        v = f({}, n),
        y = f({}, this.attachment);
      return (
        this.options.constraints.forEach(function (t) {
          var r = t.to,
            s = t.attachment,
            f = t.pin;
          'undefined' == typeof s && (s = '');
          var l = void 0,
            d = void 0;
          if (s.indexOf(' ') >= 0) {
            var c = s.split(' '),
              g = M(c, 2);
            (d = g[0]), (l = g[1]);
          } else l = d = s;
          var w = b(e, r);
          ('target' === d || 'both' === d) &&
            (o < w[1] && 'top' === v.top && ((o += u), (v.top = 'bottom')),
            o + a > w[3] && 'bottom' === v.top && ((o -= u), (v.top = 'top'))),
            'together' === d &&
              (o < w[1] &&
                'top' === v.top &&
                ('bottom' === y.top
                  ? ((o += u), (v.top = 'bottom'), (o += a), (y.top = 'top'))
                  : 'top' === y.top &&
                    ((o += u),
                    (v.top = 'bottom'),
                    (o -= a),
                    (y.top = 'bottom'))),
              o + a > w[3] &&
                'bottom' === v.top &&
                ('top' === y.top
                  ? ((o -= u), (v.top = 'top'), (o -= a), (y.top = 'bottom'))
                  : 'bottom' === y.top &&
                    ((o -= u), (v.top = 'top'), (o += a), (y.top = 'top'))),
              'middle' === v.top &&
                (o + a > w[3] && 'top' === y.top
                  ? ((o -= a), (y.top = 'bottom'))
                  : o < w[1] &&
                    'bottom' === y.top &&
                    ((o += a), (y.top = 'top')))),
            ('target' === l || 'both' === l) &&
              (i < w[0] && 'left' === v.left && ((i += p), (v.left = 'right')),
              i + h > w[2] &&
                'right' === v.left &&
                ((i -= p), (v.left = 'left'))),
            'together' === l &&
              (i < w[0] && 'left' === v.left
                ? 'right' === y.left
                  ? ((i += p), (v.left = 'right'), (i += h), (y.left = 'left'))
                  : 'left' === y.left &&
                    ((i += p), (v.left = 'right'), (i -= h), (y.left = 'right'))
                : i + h > w[2] && 'right' === v.left
                  ? 'left' === y.left
                    ? ((i -= p),
                      (v.left = 'left'),
                      (i -= h),
                      (y.left = 'right'))
                    : 'right' === y.left &&
                      ((i -= p), (v.left = 'left'), (i += h), (y.left = 'left'))
                  : 'center' === v.left &&
                    (i + h > w[2] && 'left' === y.left
                      ? ((i -= h), (y.left = 'right'))
                      : i < w[0] &&
                        'right' === y.left &&
                        ((i += h), (y.left = 'left')))),
            ('element' === d || 'both' === d) &&
              (o < w[1] && 'bottom' === y.top && ((o += a), (y.top = 'top')),
              o + a > w[3] &&
                'top' === y.top &&
                ((o -= a), (y.top = 'bottom'))),
            ('element' === l || 'both' === l) &&
              (i < w[0] && 'right' === y.left && ((i += h), (y.left = 'left')),
              i + h > w[2] &&
                'left' === y.left &&
                ((i -= h), (y.left = 'right'))),
            'string' == typeof f
              ? (f = f.split(',').map(function (t) {
                  return t.trim();
                }))
              : f === !0 && (f = ['top', 'left', 'right', 'bottom']),
            (f = f || []);
          var C = [],
            O = [];
          o < w[1] &&
            (f.indexOf('top') >= 0
              ? ((o = w[1]), C.push('top'))
              : O.push('top')),
            o + a > w[3] &&
              (f.indexOf('bottom') >= 0
                ? ((o = w[3] - a), C.push('bottom'))
                : O.push('bottom')),
            i < w[0] &&
              (f.indexOf('left') >= 0
                ? ((i = w[0]), C.push('left'))
                : O.push('left')),
            i + h > w[2] &&
              (f.indexOf('right') >= 0
                ? ((i = w[2] - h), C.push('right'))
                : O.push('right')),
            C.length &&
              !(function () {
                var t = void 0;
                (t =
                  'undefined' != typeof e.options.pinnedClass
                    ? e.options.pinnedClass
                    : e.getClass('pinned')),
                  m.push(t),
                  C.forEach(function (e) {
                    m.push(t + '-' + e);
                  });
              })(),
            O.length &&
              !(function () {
                var t = void 0;
                (t =
                  'undefined' != typeof e.options.outOfBoundsClass
                    ? e.options.outOfBoundsClass
                    : e.getClass('out-of-bounds')),
                  m.push(t),
                  O.forEach(function (e) {
                    m.push(t + '-' + e);
                  });
              })(),
            (C.indexOf('left') >= 0 || C.indexOf('right') >= 0) &&
              (y.left = v.left = !1),
            (C.indexOf('top') >= 0 || C.indexOf('bottom') >= 0) &&
              (y.top = v.top = !1),
            (v.top !== n.top ||
              v.left !== n.left ||
              y.top !== e.attachment.top ||
              y.left !== e.attachment.left) &&
              e.updateAttachClasses(y, v);
        }),
        T(function () {
          e.options.addTargetClasses !== !1 && c(e.target, m, g),
            c(e.element, m, g);
        }),
        { top: o, left: i }
      );
    },
  });
  var P = C.Utils,
    r = P.getBounds,
    c = P.updateClasses,
    T = P.defer;
  C.modules.push({
    position: function (t) {
      var e = this,
        o = t.top,
        i = t.left,
        n = this.cache('element-bounds', function () {
          return r(e.element);
        }),
        s = n.height,
        a = n.width,
        f = this.getTargetBounds(),
        h = o + s,
        l = i + a,
        d = [];
      o <= f.bottom &&
        h >= f.top &&
        ['left', 'right'].forEach(function (t) {
          var e = f[t];
          (e === i || e === l) && d.push(t);
        }),
        i <= f.right &&
          l >= f.left &&
          ['top', 'bottom'].forEach(function (t) {
            var e = f[t];
            (e === o || e === h) && d.push(t);
          });
      var u = [],
        p = [],
        g = ['left', 'top', 'right', 'bottom'];
      return (
        u.push(this.getClass('abutted')),
        g.forEach(function (t) {
          u.push(e.getClass('abutted') + '-' + t);
        }),
        d.length && p.push(this.getClass('abutted')),
        d.forEach(function (t) {
          p.push(e.getClass('abutted') + '-' + t);
        }),
        T(function () {
          e.options.addTargetClasses !== !1 && c(e.target, p, u),
            c(e.element, p, u);
        }),
        !0
      );
    },
  });
  var M = (function () {
    function t(t, e) {
      var o = [],
        i = !0,
        n = !1,
        r = void 0;
      try {
        for (
          var s, a = t[Symbol.iterator]();
          !(i = (s = a.next()).done) && (o.push(s.value), !e || o.length !== e);
          i = !0
        );
      } catch (f) {
        (n = !0), (r = f);
      } finally {
        try {
          !i && a['return'] && a['return']();
        } finally {
          if (n) throw r;
        }
      }
      return o;
    }
    return function (e, o) {
      if (Array.isArray(e)) return e;
      if (Symbol.iterator in Object(e)) return t(e, o);
      throw new TypeError(
        'Invalid attempt to destructure non-iterable instance',
      );
    };
  })();
  return (
    C.modules.push({
      position: function (t) {
        var e = t.top,
          o = t.left;
        if (this.options.shift) {
          var i = this.options.shift;
          'function' == typeof this.options.shift &&
            (i = this.options.shift.call(this, { top: e, left: o }));
          var n = void 0,
            r = void 0;
          if ('string' == typeof i) {
            (i = i.split(' ')), (i[1] = i[1] || i[0]);
            var s = i,
              a = M(s, 2);
            (n = a[0]),
              (r = a[1]),
              (n = parseFloat(n, 10)),
              (r = parseFloat(r, 10));
          } else (n = i.top), (r = i.left);
          return (e += n), (o += r), { top: e, left: o };
        }
      },
    }),
    R
  );
});

function isCertificateSelectionAvailable() {
  return iSignApplet.certificates && iSignApplet.certificates.length > 1
    ? true
    : false;
}

function browserSupportsIsignExtension() {
  var userAgent = navigator.userAgent.toLowerCase();

  if (userAgent.match(/opera|opr\//)) {
    return false;
  }

  if (userAgent.indexOf('chrome') > -1) {
    return true;
  }

  if (isEdgeBrowser()) {
    return true;
  }

  if (userAgent.indexOf('firefox') > -1) {
    var matches = userAgent.match(/firefox\/([0-9\.]+)/);
    if (matches.length > 1 && parseInt(matches[1]) >= 50) {
      return true;
    }
  }

  return false;
}

function isEdgeBrowser() {
  var userAgent = navigator.userAgent.toLowerCase();

  if (userAgent.match(/[Ee]dge\//)) {
    return true;
  }

  return false;
}

function isOsX() {
  return navigator.platform.toUpperCase().indexOf('MAC') >= 0;
}

var missingSoftwareUrl;
var missingDriversUrl;
var openEidEdgePluginDownloadUrl =
  'https://www.microsoft.com/en-us/store/p/token-signing/9n5fkx7gtdrq?rtc=1';
var isignEdgePluginDownloadUrl =
  'ms-windows-store://pdp/?ProductId=9NJXVGRB53DX';

var iSignApplet = iSignApplet || {
  isInitialized: false,
  authenticationFrameLoadedSuccessfully: false,
  scType: null,
  isUsingIsignSoftware: false,
  updateRequired: false,
  authFrameErrorChecker: null,

  parameters: {
    language: 'lt',
    debug: false,
    certificatePurpose: 'sign',
    hashAlgorithm: 'sha1',
    policyIds: null,
    policyId: '', //deprecated
    bulkSigning: false,
    allowEstonianPlugin: false,
    timeout: 90,
    codebase: window.location.protocol + '//' + window.location.host,
    jar: 'applet.jar',
    javaCheckTimeout: 25,
    supportedResidencies: [
      'lt',
      'lv',
      'ee',
      'fi',
      'e-residency',
      'be',
      'is',
      'pl',
    ],
    disabledIssuers: [],
    defaultCountry: null,
    authenticationUrls: {
      lt: null,
      fi: null,
      be: null,
      pl: null,
      is: null,
      lv: null,
      ee: null,
      'e-residency': null,
    },
  },

  init: function (parameters) {
    var _this = this;

    if (_this.isInitialized) {
      return;
    }
    _this.isInitialized = true;

    jQuery.extend(this.parameters, parameters);
    if (this.parameters.debug == true) {
      hwcrypto.isignDebug = function (msg) {
        console.log(msg);
      };
    }

    if (_this.parameters.supportedResidencies.length < 2) {
      if (_this.parameters.certificatePurpose == 'login') {
        _this.requestLogin(_this.parameters.supportedResidencies[0]);
      } else {
        _this.requestSign(_this.parameters.supportedResidencies[0]);
      }
      return;
    } else if (_this.parameters.defaultCountry) {
      if (_this.parameters.defaultCountry === 'et') {
        _this.parameters.defaultCountry = 'ee';
      }
      for (var i in _this.parameters.supportedResidencies) {
        if (
          _this.parameters.supportedResidencies[i] ===
          _this.parameters.defaultCountry
        ) {
          _this.parameters.supportedResidencies.splice(i, 1);
        }
      }
      _this.parameters.supportedResidencies.unshift(
        _this.parameters.defaultCountry,
      );
    }

    $('#isign-applet').append(
      '<ul class="sc-type-selection list-extra-spaced">\n\
            <li class="list-extra-spaced__item">\n\
                <span class="text-muted">' +
        _this.trans('select_residency') +
        '</span>\n\
                <!--<svg class="help-icon"><use xlink:href="#help_outline"></use></svg>-->\n\
                <ul class="residency-radio"></ul>\n\
            </li></ul>',
    );

    for (var i = 0; i < _this.parameters.supportedResidencies.length; i++) {
      var residency = _this.parameters.supportedResidencies[i];
      var rowContent =
        '<li class="residency-radio__item">\n\
                <label class="residency-radio__container">\n\
                <input type="radio" name="residency-radio" data-residency="' +
        residency +
        '" class="residency-radio__item__input smartcard-type-select">\n\
                    <i class="residency-radio__item__flag residency-radio__item__flag--' +
        residency +
        '"></i>\n\
                    <span class="residency-radio__item__label">' +
        _this.trans('residency.' + residency) +
        '</span>\n\
                    </label>\n\
                </li>';
      $('#isign-applet ul.residency-radio').append(rowContent);
    }

    $('.smartcard-type-select').on('change', function (e) {
      e.preventDefault();

      _this.removeAlerts();

      $('.residency-radio__item.selected').removeClass('selected');
      $(this).parents('.residency-radio__item').addClass('selected');

      var scType = $(this).data('residency');

      if (_this.parameters.certificatePurpose == 'login') {
        _this.requestLogin(scType);
      } else {
        _this.requestSign(scType);
      }
    });
  },

  isCertificateSelectionAvailable: function () {
    return iSignApplet.certificates && iSignApplet.certificates.length > 1
      ? true
      : false;
  },

  requestLogin: function (scType) {
    var _this = this;

    _this.scType = scType;
    _this.authenticationFrameLoadedSuccessfully = false;
    _this.isUsingIsignSoftware = false;

    if (scType == 'is' || scType == 'fi' || scType == 'be' || scType == 'pl') {
      missingDriversUrl = hwcrypto.missingDriversUrls[scType];
    } else {
      missingDriversUrl =
        hwcrypto.missingDriversUrls[_this.parameters.language.toLowerCase()];
    }

    switch (scType) {
      case 'lt':
      case 'fi':
      case 'be':
      case 'is':
      case 'pl':
        _this.isUsingIsignSoftware = true;
        if (isEdgeBrowser()) {
          missingSoftwareUrl = isignEdgePluginDownloadUrl;
        } else {
          missingSoftwareUrl =
            hwcrypto.missingSoftwareUrls[scType][
              _this.parameters.language.toLowerCase()
            ];
        }
        hwcrypto.chromeExtensionName = hwcrypto.chromeExtensionNames[scType];
        hwcrypto.npapiPluginName = hwcrypto.npapiPluginNames[scType];
        if (browserSupportsIsignExtension()) {
          initPlugin();
        } else {
          initJavaApplet();
        }
        break;
      case 'lv':
        hwcrypto.chromeExtensionName = hwcrypto.chromeExtensionNames[scType];
        hwcrypto.npapiPluginName = hwcrypto.npapiPluginNames[scType];
        if (isEdgeBrowser()) {
          hwcrypto.chromeExtensionName = hwcrypto.chromeExtensionNames['lt'];
          missingSoftwareUrl = isignEdgePluginDownloadUrl;
          missingDriversUrl = hwcrypto.missingDriversUrls['lv'];
        } else {
          missingSoftwareUrl =
            hwcrypto.missingSoftwareUrls[scType][
              _this.parameters.language.toLowerCase()
            ];
        }
        initPlugin();
        break;
      case 'ee':
      case 'e-residency':
        _this.startEstonianAuthentication();
        return;
    }

    iSignApplet.start();
  },

  startEstonianAuthentication: function () {
    var _this = this;

    hwcrypto.chromeExtensionName = hwcrypto.chromeExtensionNames[_this.scType];
    hwcrypto.npapiPluginName = hwcrypto.npapiPluginNames[_this.scType];

    _this.startEstonianApacheAuthentication();
  },

  startEstonianHwcryptoAuthentication: function () {
    initPlugin();

    iSignApplet.start();
  },

  startEstonianApacheAuthentication: function () {
    var _this = this;

    if (isEdgeBrowser()) {
      missingSoftwareUrl = openEidEdgePluginDownloadUrl;
    }
    if (_this.parameters.authenticationUrls[_this.scType] == null) {
      iSignApplet.showWarning(
        'This authentication type is not properly configured. Missing authentication URL.',
      );
      return;
    }

    $('#isign-applet').append(
      '<iframe id="isign-authentication" src="' +
        _this.parameters.authenticationUrls[_this.scType] +
        '" style="display:none;" onLoad="iSignApplet.onAuthenticationFrameLoad();"></iframe>',
    );
    _this.initContent();
    _this.initAuthRefreshButton();

    if (_this.isFirefox()) {
      _this.authFrameErrorChecker = setTimeout(function () {
        iSignApplet.showWarning(iSignApplet.trans('check_if_card_is_inserted'));
      }, 30000);
    }

    if (typeof Isign !== 'undefined') {
      Isign.PostMessage.init(_this.onUserAuthenticated);
    }
  },

  isFirefox: function () {
    return navigator.userAgent.search('Firefox') > -1;
  },

  isSafari: function () {
    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  },

  onAuthenticationFrameLoad: function () {
    var _this = this;
    window.clearTimeout(this.authFrameErrorChecker);
    this.removeAlerts();

    setTimeout(function () {
      if (!this.authenticationFrameLoadedSuccessfully) {
        if (_this.parameters.supportedResidencies.length < 1) {
          iSignApplet.showError(iSignApplet.trans('authentication_failed'));
          return;
        }

        if (!_this.isSafari() || !isEdgeBrowser()) {
          window.hwcrypto.getVersion().then(
            function (response) {
              if (response.indexOf('/') > -1) {
                var subversionsString = response.split('/')[0];
                var subVersions = subversionsString.split('.');
                if (
                  subVersions[0] > 0 ||
                  subVersions[1] > 0 ||
                  subVersions[2] >= 27
                ) {
                  _this.startEstonianHwcryptoAuthentication();
                  return;
                }
              } else {
                var subVersions = response.split('.');
                if (
                  subVersions[0] > 3 ||
                  (parseInt(subVersions[0]) === 3 && subVersions[1] >= 13)
                ) {
                  _this.startEstonianHwcryptoAuthentication();
                  return;
                }
              }

              iSignApplet.showError(
                iSignApplet.trans('authentication_failed_' + _this.scType),
              );
              iSignApplet.onCertificateNotFound();
            },
            function () {
              iSignApplet.showError(
                iSignApplet.trans('authentication_failed_' + _this.scType),
              );
              iSignApplet.onCertificateNotFound();
            },
          );
        } else {
          iSignApplet.showError(
            iSignApplet.trans('authentication_failed_' + _this.scType),
          );
          iSignApplet.onCertificateNotFound();
        }
      }
    }, 1000);
  },

  onUserAuthenticated: function (response) {
    this.authenticationFrameLoadedSuccessfully = true;
    if (response.status == 'ok') {
      if (
        this.authenticationTokenReceived &&
        typeof authenticationTokenReceived == 'function'
      ) {
        authenticationTokenReceived(response.token);
      }
    } else {
      iSignApplet.showError(
        iSignApplet.trans('authentication_failed_' + this.scType),
      );
    }
  },

  requestSign: function (scType) {
    var _this = this;

    _this.scType = scType;
    var selectedScType = scType;
    iSignApplet.extensionLoadingStatus = 'loading';
    hwcrypto.reset();

    if (scType == 'is' || scType == 'fi' || scType == 'be' || scType == 'pl') {
      missingDriversUrl = hwcrypto.missingDriversUrls[scType];
    } else {
      missingDriversUrl =
        hwcrypto.missingDriversUrls[_this.parameters.language.toLowerCase()];
    }

    if ((scType == 'lv' && isOsX()) || _this.parameters.bulkSigning) {
      scType = 'lt';
    }

    _this.isUsingIsignSoftware = false;
    missingSoftwareUrl =
      hwcrypto.missingSoftwareUrls[scType][
        this.parameters.language.toLowerCase()
      ];
    hwcrypto.chromeExtensionName = hwcrypto.chromeExtensionNames[scType];
    hwcrypto.npapiPluginName = hwcrypto.npapiPluginNames[scType];

    if (
      (selectedScType == 'ee' || selectedScType == 'e-residency') &&
      _this.parameters.bulkSigning
    ) {
      iSignApplet.showPermanentWarning(
        this.trans('warning.ee_bulk_signing_update') !=
          'warning.ee_bulk_signing_update'
          ? this.trans('warning.ee_bulk_signing_update')
          : '',
      );
    }

    switch (scType) {
      case 'lt':
      case 'fi':
      case 'be':
      case 'pl':
      case 'is':
        if (isEdgeBrowser()) {
          missingSoftwareUrl = isignEdgePluginDownloadUrl;
        }
        _this.isUsingIsignSoftware = true;
        if (browserSupportsIsignExtension()) {
          initPlugin();
        } else {
          initJavaApplet();
        }
        break;
      case 'lv':
      case 'ee':
      case 'e-residency':
        if (isEdgeBrowser()) {
          missingSoftwareUrl = openEidEdgePluginDownloadUrl;
        }
        initPlugin();
        break;
    }

    iSignApplet.start();
  },

  setHashAlgorithm: function (algorithm) {
    this.parameters.hashAlgorithm = algorithm;
  },

  initContent: function () {
    $('#isign-applet-content').remove();
    $('#isign-applet').append(
      '<div class="row" id="isign-applet-content">\
                <p class="applet-title">' +
        this.trans('title') +
        ':</p>\
                <span class="activate-popover">\
                    <span id="selected-certificate">' +
        this.trans('please_wait') +
        '</span> \
                    <i class="arrow" data-toggle="applet-popover" id="applet-dropdown-icon" style="display:none;"></i>\
                </span>\
                <a href="#" id="applet-loader"><span class="loader-img animate-spin">&nbsp;</span></a>\
                <p class="error" style="display:none;"></p>\
                <div class="applet-popover bottom">\
                    <div class="arrow"></div>\
                    <div class="popover-content"><ul></ul></div>\
                </div>\
            </div>',
    );
  },

  initAuthRefreshButton: function () {
    var _this = this;
    $('#applet-loader').click(function (e) {
      e.preventDefault();
      if (_this.updateRequired) {
        return;
      }
      if (_this.extensionLoadingStatus == 'unavailable') {
        _this.extensionLoadingStatus = 'loading';
        hwcrypto.probe();
      }
      if (!_this.loading) {
        if (typeof refreshClicked == 'function') {
          refreshClicked();
        }
        _this.startLoader(_this.trans('searching'));
        _this.onAuthenticationFrameLoad();
      }
    });
  },

  formatDate: function (timestamp) {
    if (!timestamp) {
      return null;
    }
    var parts = timestamp.split('.');

    return parts[2] + '-' + parts[1] + '-' + parts[0];
  },

  customIsignTranslationMethodExists: function () {
    return (
      typeof App == 'object' &&
      App != null &&
      typeof App.Translation == 'object' &&
      App.Translation != null &&
      typeof App.Translation.get == 'function'
    );
  },

  trans: function (key) {
    var result = key;

    if (
      this.customIsignTranslationMethodExists() &&
      App.Translation.get('isign_applet.' + key) != 'isign_applet.' + key
    ) {
      result = App.Translation.get('isign_applet.' + key);
    } else {
      var language = this.parameters.language
        ? this.parameters.language.toLowerCase()
        : 'lt';
      if (this.translations[language][key]) {
        result = this.translations[language][key];
      }
    }

    return result
      .replace(new RegExp('%missingDriversUrl%', 'g'), missingDriversUrl)
      .replace(new RegExp('%missingSoftwareUrl%', 'g'), missingSoftwareUrl);
  },

  getOS: function () {
    var result = 'unknown';

    if (window.navigator.userAgent.indexOf('Windows') != -1) {
      result = 'Windows';
    } else if (window.navigator.userAgent.indexOf('Mac') != -1) {
      result = 'macOS';
    } else if (window.navigator.userAgent.indexOf('Linux') != -1) {
      result = 'Linux';
    }

    return result;
  },

  is64BitOS: function () {
    var userAgent = navigator.userAgent.toLowerCase();
    if (
      userAgent.indexOf('wow64') != -1 ||
      userAgent.indexOf('win64') != -1 ||
      userAgent.indexOf('x64;') != -1 ||
      userAgent.indexOf('x86-64') != -1 ||
      userAgent.indexOf('x86_64') != -1 ||
      userAgent.indexOf('x64_64') != -1
    ) {
      return true;
    } else {
      return false;
    }
  },

  initIEFixes: function () {
    if (this.isIE() && this.getIEVersion() < 9) {
      $('#applet-loader').hide();
      $('.activate-popover').append(
        '<span class="loader-img-ie">&nbsp;</span>',
      );
    }
  },

  isIE: function () {
    var myNav = navigator.userAgent.toLowerCase();
    return myNav.indexOf('msie') != -1 ? true : false;
  },

  getIEVersion: function () {
    var myNav = navigator.userAgent.toLowerCase();
    return parseInt(myNav.split('msie')[1]);
  },

  startLoader: function (text) {
    this.loading = true;
    this.removeAlerts();
    if (text) {
      this.hideDropdownIcon();
      this.setMainText(text);
    }

    if (this.isIE() && this.getIEVersion() < 9) {
      $('#applet-loader').hide();
      $('.activate-popover .loader-img-ie').show();
    } else {
      $('#applet-loader .loader-img').addClass('animate-spin');
    }
  },

  stopLoader: function () {
    if (this.isIE() && this.getIEVersion() < 9) {
      $('.activate-popover .loader-img-ie').hide();
      $('#applet-loader').show();
    } else {
      $('#applet-loader .loader-img').removeClass('animate-spin');
    }
    this.loading = false;
  },

  showDropdownIcon: function () {
    $('#applet-dropdown-icon').show();
    $('#selected-certificate').addClass('multiple');
  },

  hideDropdownIcon: function () {
    $('#applet-dropdown-icon').hide();
    $('#selected-certificate').removeClass('multiple');
  },

  isPopoverAvailable: function () {
    if ($('#applet-dropdown-icon:visible').length > 0) {
      return true;
    } else {
      return false;
    }
  },

  printPopoverRow: function (container, certificate) {
    container.append(
      '<li class="select-certificate" data-id="' +
        certificate['id'] +
        '">' +
        certificate['name'] +
        ' / ' +
        certificate['issuer'] +
        ' / ' +
        (certificate['valid']
          ? this.trans('valid_to') +
            ': ' +
            this.formatDate(certificate['validTo'])
          : '<span class="alert-danger">' +
            this.trans('certificate_not_valid') +
            '</span>') +
        '</li>',
    );
  },

  getMainCertificateText: function (certificate) {
    return (
      certificate['name'] +
      ' / ' +
      certificate['issuer'] +
      (certificate['validTo']
        ? ' / ' +
          this.trans('valid_to') +
          ': ' +
          this.formatDate(certificate['validTo'])
        : '')
    );
  },

  setMainText: function (msg) {
    $('#isign-applet #selected-certificate').html(msg);
  },

  log: function (logString) {
    // override this function if log is needed
  },

  showWarning: function (message) {
    this.showAlert(message, 'warning');
    if (this.parameters.certificatePurpose != 'login') {
      $('.residency-radio__item.selected').addClass('warning');
    } else {
      $('[name="residency-radio"]:checked').removeAttr('checked');
    }
  },

  showPermanentWarning: function (message) {
    if (!message) {
      return;
    }
    $('.permanent-alert').remove();
    var alert = this.showAlert(message, 'warning');
    alert.addClass('permanent-alert');
  },

  showError: function (message) {
    this.showAlert(message, 'error');
    if (this.parameters.certificatePurpose != 'login') {
      $('.residency-radio__item.selected').addClass('error');
    } else {
      $('[name="residency-radio"]:checked').removeAttr('checked');
    }
  },

  showAlert: function (message, alertType) {
    var _this = this;
    var element;

    _this.removeAlerts();

    if ($('#isign-applet .residency-radio').length > 0) {
      element = $('#isign-applet .residency-radio');
    } else {
      if ($('#isign-applet #isign-applet-content').length == 0) {
        $('#isign-applet').html('<div id="isign-applet-content"></div>');
      }
      element = $('#isign-applet #isign-applet-content');
    }

    var alert = $(
      '<div class="input-holder__alert input-holder__alert--' +
        alertType +
        '">\
            <svg class="input-holder__alert__icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#error"></use></svg>\
            <span class="input-holder__alert__text">' +
        message +
        '</span>\
            <span class="input-holder__alert__cancel"></span>\
        </div>',
    );

    element.before(alert);

    $('.input-holder__alert__cancel').click(function () {
      _this.removeAlerts();
    });

    return alert;
  },

  removeAlerts: function () {
    $('#isign-applet .input-holder__alert').not('.permanent-alert').remove();
    $('.residency-radio__item').removeClass('error');
    $('.residency-radio__item').removeClass('warning');
  },

  onCertificateNotFound: function (driversList) {
    iSignApplet.setMainText(this.trans('certificate_not_selected'));
    iSignApplet.stopLoader();

    if (!this.isUsingIsignSoftware || !driversList || driversList.length == 0) {
      this.showNoDevicesError();
      return;
    }

    if (
      (this.scType == 'ee' || this.scType == 'e-residency') &&
      !this.areEstonianDriversInstalled(driversList)
    ) {
      iSignApplet.showWarning(this.trans('download_ee_sc_drivers'));
    } else if (
      this.scType == 'lv' &&
      !this.areLatvianDriversInstalled(driversList)
    ) {
      iSignApplet.showWarning(this.trans('download_lv_sc_drivers'));
    } else if (
      this.scType == 'fi' &&
      !this.areFinishDriversInstalled(driversList)
    ) {
      iSignApplet.showWarning(this.trans('download_fi_sc_drivers'));
    } else if (
      this.scType == 'lt' &&
      !this.areLithuanianDriversInstalled(driversList)
    ) {
      iSignApplet.showWarning(this.trans('download_lt_sc_drivers'));
    } else {
      this.showNoDevicesError();
    }
  },

  showNoDevicesError: function () {
    if (
      this.scType == 'lt' ||
      this.scType == 'fi' ||
      this.scType == 'be' ||
      this.scType == 'is' ||
      this.scType == 'pl'
    ) {
      if (this.isSafari()) {
        iSignApplet.showError(this.trans('no_devices_safari'));
      } else {
        iSignApplet.showError(this.trans('no_devices_' + this.scType));
      }
    } else {
      if (
        this.parameters.bulkSigning &&
        (this.scType == 'ee' || this.scType == 'e-residency')
      ) {
        missingDriversUrl = hwcrypto.missingDriversUrlsForBatchSigning['et'];
        iSignApplet.showError(this.trans('no_devices_ee_batch'));
        return;
      }

      if (this.parameters.bulkSigning && this.isSafari()) {
        iSignApplet.showError(this.trans('no_devices_safari'));
      } else {
        iSignApplet.showError(this.trans('no_devices_' + this.scType));
      }
    }
  },

  areLithuanianDriversInstalled: function (driversList) {
    var matchingKeys = ['eTPkcs11', 'gclib', 'aetpkss', 'ccpkip11'];

    return this.areDriversInstalled(driversList, matchingKeys);
  },

  areEstonianDriversInstalled: function (driversList) {
    var matchingKeys = ['esteid-pkcs11', 'opensc-pkcs11'];

    return this.areDriversInstalled(driversList, matchingKeys);
  },

  areFinishDriversInstalled: function (driversList) {
    var matchingKeys = ['cryptoki'];

    return this.areDriversInstalled(driversList, matchingKeys);
  },

  areLatvianDriversInstalled: function (driversList) {
    var matchingKeys = ['OTLvP11', 'otlv-pkcs11'];

    return this.areDriversInstalled(driversList, matchingKeys);
  },

  areDriversInstalled: function (installedDrivers, matchingKeys) {
    var requiredDriverFound = false;
    for (var installedDriverIndex in installedDrivers) {
      var driverPath = installedDrivers[installedDriverIndex];
      for (var matchingKeyIndex in matchingKeys) {
        if (
          driverPath
            .toLowerCase()
            .indexOf(matchingKeys[matchingKeyIndex].toLowerCase()) > -1
        ) {
          requiredDriverFound = true;
        }
      }
    }

    return requiredDriverFound;
  },

  translations: {
    en: {
      please_wait: 'Please wait...',
      title: 'E-signature',
      loading_applet: 'Please wait, loading plug-in.',
      searching: 'Please wait, searching for devices.',
      no_devices:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      no_devices_lt:
        'Could not find any devices, please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>.',
      no_devices_lv:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      'no_devices_e-residency':
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      no_devices_ee:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      no_devices_ee_batch:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      no_devices_fi:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      no_devices_be:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      no_devices_pl:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      no_devices_is:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      no_devices_without_link: 'Could not find any devices, try again.',
      failed_to_load_applet:
        'Could not find any devices, try again. <a target="_blank" href="%missingDriversUrl%">Device drivers can be downloaded here</a>.',
      certificate_not_valid: 'Certificate is not valid.',
      valid_to: 'Valid to',
      signing: 'Please wait, signing.',
      authenticating: 'Please wait, authenticating.',
      pin_blocked: 'PIN code was blocked.',
      signing_failed: 'Signing failed, please try again.',
      no_implementation:
        'Missing required software. <a target="_blank" href="%missingSoftwareUrl%">Download here</a>',
      no_implementation_for_bulk_signing:
        'Missing required software. <a target="_blank" href="%missingSoftwareUrl%">Download here</a>',
      certificate_without_info_selected: 'Certificate selected.',
      check_java:
        'Unable to start plugin. <a href="https://www.java.com/en/download/installed.jsp">Please check if Java is installed.</a>',
      'residency.lt': 'Lithuania',
      'residency.fi': 'Finland',
      'residency.lv': 'Latvia',
      'residency.ee': 'Estonia',
      'residency.e-residency': 'E-Residency',
      'residency.be': 'Belgium',
      'residency.pl': 'Poland',
      'residency.is': 'Iceland',
      certificate_not_selected: 'Certificate is not selected',
      please_enable_plugin:
        'Please enable browser plugin and refresh this page.',
      download_ee_sc_drivers:
        'Additional drivers are needed for signing. They can be downloaded here: <a target="_blank" href="%missingDriversUrl%#country-estonia">%missingDriversUrl%</a>',
      download_lv_sc_drivers:
        'Additional drivers are needed for signing. They can be downloaded here: <a target="_blank" href="%missingDriversUrl%#country-latvia">%missingDriversUrl%</a>',
      download_lt_sc_drivers:
        'Additional drivers are needed for signing. They can be downloaded here: <a target="_blank" href="%missingDriversUrl%#country-lithuania">%missingDriversUrl%</a>',
      download_fi_sc_drivers:
        'Additional drivers are needed for signing. They can be downloaded here: <a target="_blank" href="%missingDriversUrl%">%missingDriversUrl%</a>',
      download_is_sc_drivers:
        'Additional drivers are needed for signing. They can be downloaded here: <a target="_blank" href="https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/">https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/</a>',
      authentication_failed:
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      authentication_failed_lt:
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      authentication_failed_lv:
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      authentication_failed_ee:
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      'authentication_failed_e-residency':
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      authentication_failed_be:
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      authentication_failed_pl:
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      authentication_failed_is:
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      authentication_failed_fi:
        'Authentication failed. Please try again. <a target="_blank" href="%missingDriversUrl%">More information can be found here</a>',
      update_required:
        'You need to update your Google Chrome extension. It can be downloaded here: <a target="_blank" href="%missingSoftwareUrl%">%missingSoftwareUrl%</a>',
      check_if_card_is_inserted:
        'Please check if ID card is inserted correctly.',
      select_residency: 'Select certificate issuer country',
      no_devices_safari:
        'Could not find any devices. You need to set custom permissions in order to use stationary signature with Safari browser. Go to "Safari preferences" -> "Security tab" -> "Internet plug-ins" -> "Website Settings" -> "Java tab" and select "Run in Unsafe Mode" next to this page name.',
      browser_not_supported: 'Your browser is not supported',
      'warning.ee_bulk_signing_update':
        'If signing fails, make sure you have latest software installed. Otherwise, please update browser extension from <a target="_blank" href="%missingSoftwareUrl%">%missingSoftwareUrl%</a> and smartcard drivers from <a target="_blank" href="%missingDriversUrl%">%missingDriversUrl%</a>.',
    },
    et: {
      please_wait: 'Palun oota...',
      title: 'Digitaalne allkiri',
      loading_applet: 'Palun oota, pistikprogrammi laetakse.',
      searching: 'Palun oota, otsin seadmeid.',
      no_devices:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_lt:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_lv:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      'no_devices_e-residency':
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_ee:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_ee_batch:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_fi:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_be:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_pl:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_is:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      no_devices_without_link: 'Seadmeid ei leitud, palun proovi uuesti.',
      failed_to_load_applet:
        'Seadmeid ei leitud, palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadme draiveri saab alla laadida siit</a>.',
      certificate_not_valid: 'Sertifikaat ei ole kehtiv.',
      valid_to: 'Kehtiv kuni',
      signing: 'Palun oota, allkirjastamine toimub.',
      authenticating: 'Palun oota, autendin.',
      pin_blocked: 'PIN kood on blokeeritud.',
      signing_failed: 'Allkirjastamine ebaõnnestus, proovi palun uuesti.',
      no_implementation:
        'Vajalik tarkvara puudub. <a target="_blank" href="%missingSoftwareUrl%">Lae siit alla</a>',
      no_implementation_for_bulk_signing:
        'Vajalik tarkvara puudub. <a target="_blank" href="%missingSoftwareUrl%">Lae siit alla</a>',
      certificate_without_info_selected: 'Sertifikaat valitud.',
      check_java:
        'Plugina käivitamine ebaõnnestub. <a href="https://www.java.com/en/download/installed.jsp">Palun kontrolli Java olemasolu.</a>',
      'residency.lt': 'Leedu',
      'residency.fi': 'Soome',
      'residency.lv': 'Läti',
      'residency.ee': 'Eesti',
      'residency.e-residency': 'E-resident',
      'residency.be': 'Belgia',
      'residency.pl': 'Poola',
      'residency.is': 'Island',
      certificate_not_selected: 'Sertifikaat ei ole valitud',
      please_enable_plugin:
        'Palun aktiveeri pistikprogramm ja uuenda veebilehte.',
      download_ee_sc_drivers:
        'Allkirjastamiseks on vajalikud lisadraiverid. Need saab alla laadida siit: <a target="_blank" href="%missingDriversUrl%#country-estonia">%missingDriversUrl%</a>',
      download_lv_sc_drivers:
        'Allkirjastamiseks on vajalikud lisadraiverid. Need saab alla laadida siit: <a target="_blank" href="%missingDriversUrl%#country-latvia">%missingDriversUrl%</a>',
      download_lt_sc_drivers:
        'Allkirjastamiseks on vajalikud lisadraiverid. Need saab alla laadida siit: <a target="_blank" href="%missingDriversUrl%#country-lithuania">%missingDriversUrl%</a>',
      download_fi_sc_drivers:
        'Allkirjastamiseks on vajalikud lisadraiverid. Need saab alla laadida siit: <a target="_blank" href="%missingDriversUrl%">%missingDriversUrl%</a>',
      download_is_sc_drivers:
        'Allkirjastamiseks on vajalikud lisadraiverid. Need saab alla laadida siit: <a target="_blank" href="https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/">https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/</a>',
      authentication_failed:
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      authentication_failed_lt:
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      authentication_failed_lv:
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      authentication_failed_ee:
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      'authentication_failed_e-residency':
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      authentication_failed_fi:
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      authentication_failed_be:
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      authentication_failed_pl:
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      authentication_failed_is:
        'Autentimine ebaõnnestus. Palun proovi uuesti. <a target="_blank" href="%missingDriversUrl%">Seadmete draiverit saab alla laadida siit</a>.',
      update_required:
        'Vajalik on teie Google Chrome veebilehitseja laiendi uuendamine. Saate selle alla laadida siit: <a target="_blank" href="%missingSoftwareUrl%">%missingSoftwareUrl%</a>',
      check_if_card_is_inserted:
        'Palun kontrolli, kas ID kaart on korrektselt sisestatud.',
      select_residency: 'Palun vali oma seadme tüüp.',
      no_devices_safari:
        'Seadmeid ei leitud. Pead kohandama veebilehitseja õiguseid kasutamaks seda allkirjastamisel. Mine "Safari preferences" → "Security tab" → "Internet plug-ins" → "Website Settings" → "Java tab" ja vali lehe nimetuse kõrvalt "Run in Unsafe Mode"',
      browser_not_supported: 'Teie brauser ei toeta',
    },
    ru: {
      please_wait: 'Подождите…',
      title: 'Эл. подпись',
      loading_applet: 'Подождите, загружается дополнение.',
      searching: 'Подождите, ведётся поиск устройств.',
      no_devices:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_lt:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_lv:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_ee:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_ee_batch:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      'no_devices_e-residency':
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_fi:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_be:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_pl:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_is:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      no_devices_without_link: 'Устройства не найдены, попробуйте ещё раз.',
      failed_to_load_applet:
        'Устройства не найдены, попробуйте ещё раз. <a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      certificate_not_valid: 'Сертификат недействителен.',
      valid_to: 'Действителен по',
      signing: 'Подождите, идёт подпись.',
      authenticating: 'Подождите, идёт авторизация.',
      pin_blocked: 'PIN код был заблокирован.',
      signing_failed: 'Подпись не удалась, попробуйте ещё раз.',
      no_implementation:
        'Не хватает программного обеспечения. <a target="_blank" href="%missingSoftwareUrl%">Загрузить</a>',
      no_implementation_for_bulk_signing:
        'Не хватает программного обеспечения. <a target="_blank" href="%missingSoftwareUrl%">Загрузить</a>',
      certificate_without_info_selected: 'Сертификат выбран.',
      check_java:
        'Невозможно загрузить дополнение. <a href="https://www.java.com/ru/download/installed.jsp">Проверье, установлена ли у вас Java.</a>',
      'residency.lt': 'Литва',
      'residency.fi': 'Финляндия',
      'residency.lv': 'Латвия',
      'residency.ee': 'Эстония',
      'residency.e-residency': 'E-резиденция',
      'residency.be': 'Бельгия',
      'residency.pl': 'Польша',
      'residency.is': 'Исландия',
      certificate_not_selected: 'Сертификат не выбран',
      please_enable_plugin:
        'Пожалуйста, разрешите дополнение и перезагрузите страницу.',
      download_ee_sc_drivers:
        'Необходимо установить дополнительные драйвера. Их можно скачать отсюда: <a target="_blank" href="%missingDriversUrl%#country-estonia">%missingDriversUrl%</a>',
      download_lv_sc_drivers:
        'Необходимо установить дополнительные драйвера. Их можно скачать отсюда: <a target="_blank" href="%missingDriversUrl%#country-latvia">%missingDriversUrl%</a>',
      download_lt_sc_drivers:
        'Необходимо установить дополнительные драйвера. Их можно скачать отсюда: <a target="_blank" href="%missingDriversUrl%#country-lithuania">%missingDriversUrl%</a>',
      download_fi_sc_drivers:
        'Необходимо установить дополнительные драйвера. Их можно скачать отсюда: <a target="_blank" href="%missingDriversUrl%">%missingDriversUrl%</a>',
      download_is_sc_drivers:
        'Необходимо установить дополнительные драйвера. Их можно скачать отсюда: <a target="_blank" href="https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/">https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/</a>',
      authentication_failed:
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      authentication_failed_lt:
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      authentication_failed_lv:
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      authentication_failed_ee:
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      'authentication_failed_e-residency':
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      authentication_failed_fi:
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      authentication_failed_be:
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      authentication_failed_pl:
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      authentication_failed_is:
        'Подключение невозможно. Попробуйте ещё раз.<a target="_blank" href="%missingDriversUrl%">Драйвера устройств можете найти здесь</a>.',
      update_required:
        'Необходимо обновление браузера Google Chrome. Его можно скачать отсюда: <a target="_blank" href="%missingSoftwareUrl%">%missingSoftwareUrl%</a>',
      check_if_card_is_inserted: 'Убедиесь в правильности установки карточки.',
      select_residency: 'Выберите тип устройства',
      no_devices_safari:
        'Устройства не найдены. Желая использовать стационарную подпись для Safari, вам необходимо выполнить дополнительные действия. Откройте "Safari preferences" → "Security" → "Internet plug-ins" → "Website Settings" → "Java" и выберите "Run in Unsafe Mode" возле данной страницы.',
      browser_not_supported: 'Ваш браузер не поддерживается',
    },
    lv: {
      please_wait: 'Lūdzu, uzgaidiet...',
      title: 'E-paraksts',
      loading_applet: 'Lūdzu, uzgaidiet, notiek spraudņa ielāde.',
      searching: 'Lūdzu, uzgaidiet, notiek ierīču meklēšana.',
      no_devices:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_lt:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_lv:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_ee:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_ee_batch:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      'no_devices_e-residency':
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_fi:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_be:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_pl:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_is:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      no_devices_without_link:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz.',
      failed_to_load_applet:
        'Nevarēja atrast nevienu ierīci, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      certificate_not_valid: 'Sertifikāts nav derīgs.',
      valid_to: 'Derīgs līdz',
      signing: 'Lūdzu, uzgaidiet, notiek parakstīšana.',
      authenticating: 'Lūdzu, uzgaidiet, notiek autentificēšana.',
      pin_blocked: 'PIN kods tika bloķēts.',
      signing_failed: 'Parakstīšanas kļūme, lūdzu, mēģiniet vēlreiz.',
      no_implementation:
        'Trūkst obligātās programmatūras. <a target="_blank" href="%missingSoftwareUrl%">Lejupielādēt šeit</a>',
      no_implementation_for_bulk_signing:
        'Trūkst obligātās programmatūras. <a target="_blank" href="%missingSoftwareUrl%">Lejupielādēt šeit</a>',
      certificate_without_info_selected: 'Sertifikāts ir atlasīts.',
      check_java:
        'Neizdevās palaist spraudni. <a href="https://www.java.com/en/download/installed.jsp">Lūdzu, pārbaudiet, vai ir instalēta Java.</a>',
      'residency.lt': 'Lietuva',
      'residency.fi': 'Somija',
      'residency.lv': 'Latvija',
      'residency.ee': 'Igaunija',
      'residency.e-residency': 'E-rezidentūra',
      'residency.be': 'Beļģija',
      'residency.pl': 'Polija',
      'residency.is': 'Īslande',
      certificate_not_selected: 'Sertifikāts nav atlasīts.',
      please_enable_plugin:
        'Lūdzu, iespējojiet pārlūkprogrammas spraudni un atsvaidziniet šo lapu.',
      download_ee_sc_drivers:
        'Parakstīšanai ir nepieciešami papildu draiveri. Tos var lejupielādēt šeit: <a target="_blank" href="%missingDriversUrl%#country-estonia">%missingDriversUrl%</a>',
      download_lv_sc_drivers:
        'Parakstīšanai ir nepieciešami papildu draiveri. Tos var lejupielādēt šeit: <a target="_blank" href="%missingDriversUrl%#country-latvia">%missingDriversUrl%</a>',
      download_lt_sc_drivers:
        'Parakstīšanai ir nepieciešami papildu draiveri. Tos var lejupielādēt šeit: <a target="_blank" href="%missingDriversUrl%#country-lithuania">%missingDriversUrl%</a>',
      download_fi_sc_drivers:
        'Parakstīšanai ir nepieciešami papildu draiveri. Tos var lejupielādēt šeit: <a target="_blank" href="%missingDriversUrl%">%missingDriversUrl%</a>',
      download_is_sc_drivers:
        'Parakstīšanai ir nepieciešami papildu draiveri. Tos var lejupielādēt šeit: <a target="_blank" href="https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/">https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/</a>',
      authentication_failed:
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      authentication_failed_lt:
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      authentication_failed_lv:
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      authentication_failed_ee:
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      'authentication_failed_e-residency':
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      authentication_failed_fi:
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      authentication_failed_be:
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      authentication_failed_pl:
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      authentication_failed_is:
        'Autentifikācijas kļūme. Lūdzu, mēģiniet vēlreiz. <a target="_blank" href="%missingDriversUrl%">Ierīces draiverus var lejupielādēt šeit</a>.',
      update_required:
        'Jāatjaunina paplašinājums. To var lejupielādēt šeit: <a target="_blank" href="%missingSoftwareUrl%">%missingSoftwareUrl%</a>',
      check_if_card_is_inserted:
        'Lūdzu, pārbaudi, vai ID karte ir pareizi ievietota.',
      select_residency: 'Atlasīt sertifikāta izdevējvalsti',
      no_devices_safari:
        'Nevarēja atrast nevienu ierīci. Lai pārlūkprogrammā Safari izmantotu stacionāro parakstu, ir jāiestata pielāgotas atļaujas. Atveriet Safari preferences > Security tab (cilne Drošība) > Internet plug-ins (Interneta spraudņi) > Website Settings (Vietnes iestatījumi) > Java tab (cilne Java) un blakus tālāk norādītajam lapas nosaukumam atlasiet Run in Unsafe Mode (Palaist nedrošajā režīmā).',
      browser_not_supported: 'Jūsu pārlūkprogramma netiek atbalstīta',
    },
    is: {
      please_wait: 'Vinsamlegast bíðið ...',
      title: 'Rafrænar undirskriftir',
      loading_applet: 'Vinsamlegast bíðið, hleð inn hugbúnaði.',
      searching: 'Vinsamlegast bíðið, leita að kortalesara.',
      no_devices:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_lt:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_lv:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_ee:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_ee_batch:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      'no_devices_e-residency':
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_fi:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_be:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_pl:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_is:
        'Ekki tókst að finna neinn kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      no_devices_without_link: 'Ekki tókst að finna kortalesara. Reynið aftur.',
      failed_to_load_applet:
        'Ekki tókst að finna kortalesara. Reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      certificate_not_valid: 'Skilríkin eru ekki gild.',
      valid_to: 'Gild til',
      signing: 'Vinsamlegast bíðið, undirritun.',
      authenticating: 'Vinsamlegast bíðið, auðkenning.',
      pin_blocked: 'PIN númeri var læst.',
      signing_failed: 'Undirritun mistókst. Vinsamlegast reynið aftur.',
      no_implementation:
        'Nauðsynlegan hugbúnað vantar. <a target="_blank" href="%missingSoftwareUrl%">Sækið hann hér</a>',
      no_implementation_for_bulk_signing:
        'Nauðsynlegan hugbúnað vantar. <a target="_blank" href="%missingSoftwareUrl%">Sækið hann hér</a>',
      certificate_without_info_selected: 'Skilríki valið.',
      check_java:
        'Ekki tókst að ræsa hugbúnað. <a href="https://www.java.com/en/download/installed.jsp">Vinsamlegast kannið hvort Java sé uppsett.</a>',
      'residency.lt': 'Litáen',
      'residency.fi': 'Finnland',
      'residency.lv': 'Lettland',
      'residency.ee': 'Eistland',
      'residency.e-residency': 'raf-Búseta',
      'residency.be': 'Belgía',
      'residency.pl': 'Pólland',
      'residency.is': 'Ísland',
      certificate_not_selected: 'Skilríki eru ekki valin',
      please_enable_plugin:
        'Vinsamlegast virkið hugbúnað í vafranum og endurhlaðið þessari síðu.',
      download_ee_sc_drivers:
        'Viðbótar rekla þarf til þess að undirrita. Hægt er að sækja þá hér: <a target="_blank" href="%missingDriversUrl%#country-estonia">%missingDriversUrl%</a>',
      download_lv_sc_drivers:
        'Viðbótar rekla þarf til þess að undirrita. Hægt er að sækja þá hér: <a target="_blank" href="%missingDriversUrl%#country-latvia">%missingDriversUrl%</a>',
      download_lt_sc_drivers:
        'Viðbótar rekla þarf til þess að undirrita. Hægt er að sækja þá hér: <a target="_blank" href="%missingDriversUrl%#country-lithuania">%missingDriversUrl%</a>',
      download_fi_sc_drivers:
        'Viðbótar rekla þarf til þess að undirrita. Hægt er að sækja þá hér: <a target="_blank" href="%missingDriversUrl%">%missingDriversUrl%</a>',
      download_is_sc_drivers:
        'Viðbótar rekla þarf til þess að undirrita. Hægt er að sækja þá hér: <a target="_blank" href="https://www.audkenni.is/rafraen-skilriki/nexus-hugbunadur/">https://www.audkenni.is/rafraen-skilriki/nexus-hugbunadur/</a>',
      authentication_failed:
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      authentication_failed_lt:
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      authentication_failed_lv:
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      authentication_failed_ee:
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      'authentication_failed_e-residency':
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      authentication_failed_fi:
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      authentication_failed_be:
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      authentication_failed_pl:
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      authentication_failed_is:
        'Auðkenning mistókst. Vinsamlegast reynið aftur. <a target="_blank" href="%missingDriversUrl%">Hægt er að sækja rekla fyrir kortalesara hér</a>.',
      update_required:
        'Þú þarf að uppfæra Google Chrome viðbótarhugbúnaðinn. Hægt er að sækja hann hér: <a target="_blank" href="%missingSoftwareUrl%">%missingSoftwareUrl%</a>',
      check_if_card_is_inserted:
        'Vinsamlegast athugið hvort kortið hafi veirð sett rétt í lesarann.',
      select_residency: 'Veljið land útgefanda skilríkjanna',
      no_devices_safari:
        'Ekki tókst að finna neinn lesara. Þú þarft að gefa heimild í stillingu til þess að geta notað staðbundnar undirskriftir með Safari vafranum. Farið í "Safari preferences" → "Security tab" → "Internet plug-ins" → "Website Settings" → "Java tab" og veljið "Run in Unsafe Mode" við hliðina á nafninu á þessari síðu.',
      browser_not_supported: 'Vafrinn þinn er ekki studdur',
    },
    lt: {
      please_wait: 'Palaukite…',
      title: 'El. parašas',
      loading_applet: 'Palaukite, paleidžiamas papildinys.',
      searching: 'Palaukite, ieškoma įrenginių.',
      no_devices:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_lt:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_lv:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_ee:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_ee_batch:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      'no_devices_e-residency':
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_fi:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_be:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_pl:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_is:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      no_devices_without_link: 'Įrenginių rasti nepavyko, bandykite iš naujo.',
      failed_to_load_applet:
        'Įrenginių rasti nepavyko, bandykite iš naujo. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      certificate_not_valid: 'Sertifikatas nebegalioja.',
      valid_to: 'Galioja iki',
      signing: 'Palaukite, vykdomas pasirašymas.',
      authenticating: 'Palaukite, prisijungiama.',
      pin_blocked: 'PIN kodas buvo užblokuotas.',
      signing_failed: 'Pasirašymas nepavyko, bandykite dar kartą.',
      no_implementation:
        'Trūksta programinės įrangos. <a target="_blank" href="%missingSoftwareUrl%">Atsisiųsti čia</a>',
      no_implementation_for_bulk_signing:
        'Trūksta programinės įrangos. <a target="_blank" href="%missingSoftwareUrl%">Atsisiųsti čia</a>',
      certificate_without_info_selected: 'Sertifikatas pasirinktas.',
      check_java:
        'Nepavyksta paleisti papildinio. <a href="https://www.java.com/en/download/installed.jsp">Patikrinkite ar Jūsų kompiuteryje įdiegta „Java“.</a>',
      'residency.lt': 'Lietuva',
      'residency.fi': 'Suomija',
      'residency.lv': 'Latvija',
      'residency.ee': 'Estija',
      'residency.e-residency': 'E-Rezidencija',
      'residency.be': 'Belgija',
      'residency.pl': 'Lenkija',
      'residency.is': 'Islandija',
      certificate_not_selected: 'Sertifikatas nepasirinktas',
      please_enable_plugin: 'Prašome įgalinti plėtinį ir perkrauti ši puslapį.',
      download_ee_sc_drivers:
        'Jums reikia įsidiegti papildomas tvarkykles. Jas galite parsisiųsti iš <a target="_blank" href="%missingDriversUrl%#country-estonia">%missingDriversUrl%</a>',
      download_lv_sc_drivers:
        'Jums reikia įsidiegti papildomas tvarkykles. Jas galite parsisiųsti iš <a target="_blank" href="%missingDriversUrl%#country-latvia">%missingDriversUrl%</a>',
      download_lt_sc_drivers:
        'Jums reikia įsidiegti papildomas tvarkykles. Jas galite parsisiųsti iš <a target="_blank" href="%missingDriversUrl%#country-lithuania">%missingDriversUrl%</a>',
      download_fi_sc_drivers:
        'Jums reikia įsidiegti papildomas tvarkykles. Jas galite parsisiųsti iš <a target="_blank" href="%missingDriversUrl%">%missingDriversUrl%</a>',
      download_is_sc_drivers:
        'Jums reikia įsidiegti papildomas tvarkykles. Jas galite parsisiųsti iš <a target="_blank" href="https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/">https://www.audkenni.is/en/electronic-certificates/nexus-hugbunadur-en/</a>',
      authentication_failed:
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      authentication_failed_lt:
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      authentication_failed_lv:
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      authentication_failed_ee:
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      'authentication_failed_e-residency':
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      authentication_failed_fi:
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      authentication_failed_be:
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      authentication_failed_pl:
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      authentication_failed_is:
        'Prisijungimas nepavyko. Bandykite dar kartą. <a target="_blank" href="%missingDriversUrl%">Įrenginio tvarkykles galite rasti čia</a>.',
      update_required:
        'Reikalingas „Google Chrome“ naršyklės plėtinio atnaujinimas. Jį parsisiųsti galite čia: <a target="_blank" href="%missingSoftwareUrl%">%missingSoftwareUrl%</a>',
      check_if_card_is_inserted: 'Patikrinkite ar teisingai įstatėte kortelę.',
      select_residency: 'Pasirinkite sertifikatą išdavusią šalį',
      no_devices_safari:
        'Įrenginių rasti nepavyko. Jei norite naudotis stacionariu parašu „Safari“ naršyklėje, turite atlikti papildomus veiksmus. Atverkite „Safari“ nustatymus, juose pasirinkite kortelę „Security“, tada pasirinkite „Internet plug-ins“, tada „Website Settings“, tada skirtuką „Java“ ir jame pažymėkite „Run in Unsafe Mode“ ties šio portalo pavadinimu.',
      browser_not_supported: 'Jūsų naudojama naršyklė šiuo metu nepalaikoma.',
    },
  },
};

function initPlugin() {
  iSignApplet = $.extend(iSignApplet, {
    selectedCertificate: null,
    signatureValue: null,
    extensionLoadingStatus: 'loading', // loading | loaded | unavailable
    pluginLoadingTimeout: 5, //seconds

    start: function () {
      hwcrypto.probe();
      this.initContent();
      this.initRefreshButton();
      this.loadCertificatesWithPlugin(true);
    },

    verify: function (parameters) {
      jQuery.extend(this.parameters, parameters);
      this.verifyInstallation();
    },

    repaint: function () {
      var popoverContainer = $('#isign-applet .applet-popover ul');
      popoverContainer.html('');

      if (this.selectedCertificate) {
        if (this.selectedCertificate['name']) {
          this.setMainText(
            this.getMainCertificateText(this.selectedCertificate),
          );
        } else {
          this.setMainText(this.trans('certificate_without_info_selected'));
        }
        if (this.selectedCertificate['valid'] == false) {
          iSignApplet.showError(this.trans('certificate_not_valid'));
        }
      } else {
        iSignApplet.showError(this.trans('no_devices_' + this.scType));
        this.setMainText(this.trans('certificate_not_selected'));
        this.stopLoader();
      }
    },

    initRefreshButton: function () {
      var _this = this;
      $('#applet-loader').click(function (e) {
        e.preventDefault();
        if (_this.updateRequired) {
          return;
        }
        if (_this.extensionLoadingStatus == 'unavailable') {
          _this.extensionLoadingStatus = 'loading';
          hwcrypto.probe();
        }
        if (!_this.loading) {
          if (typeof refreshClicked == 'function') {
            refreshClicked();
          }
          _this.startLoader(_this.trans('searching'));
          _this.loadCertificatesWithPlugin();
        }
      });
    },

    verifyInstallation: function () {
      var _this = this;
      if (_this.extensionLoadingStatus == 'loading') {
        setTimeout(function () {
          _this.verifyInstallation();
        }, 1000);
        return;
      }
      window.hwcrypto.getVersion().then(
        function (response) {
          if (response) {
            _this.verified('ok');
            return;
          }
          _this.verified('unknown');
        },
        function (err) {
          try {
            var response = JSON.parse(err.message);
            if (response.result == 'no_implementation') {
              _this.verified('no_application');
              return;
            }
          } catch (exception) {
            var response = {};
            response.result = err.message;
            if (response.result == 'no_implementation') {
              _this.verified('no_extension');
              return;
            }
          }
          _this.verified('unknown');
        },
      );
    },

    verified: function (result) {
      if (typeof isignExtensionVerified == 'function') {
        isignExtensionVerified(result);
      }
    },

    loadCertificatesWithPlugin: function (isInitial) {
      if (typeof isInitial === 'undefined') {
        isInitial = false;
      }

      var _this = this;
      if (_this.extensionLoadingStatus == 'loading') {
        setTimeout(function () {
          _this.loadCertificatesWithPlugin(isInitial);
        }, 1000);
        return;
      }
      if (
        (!hwcrypto.hasExtensionFor(hwcrypto.chromeExtensionName) &&
          !hwcrypto.hasPluginFor(hwcrypto.npapiPluginName)) ||
        _this.extensionLoadingStatus == 'unavailable'
      ) {
        if (_this.pluginLoadingTimeout > 0) {
          _this.extensionLoadingStatus = null;
          _this.pluginLoadingTimeout--;
          setTimeout(function () {
            _this.loadCertificatesWithPlugin(isInitial);
          }, 1000);
        } else {
          if (
            hwcrypto.hasPluginFor(hwcrypto.npapiPluginName) &&
            _this.isFirefox() &&
            (_this.scType == 'lv' ||
              _this.scType == 'ee' ||
              _this.scType == 'e-residency')
          ) {
            iSignApplet.showError(_this.trans('please_enable_plugin'));
            return;
          }

          extensionUnavailable();
        }

        return;
      }

      _this.startLoader(_this.trans('searching'));

      var parameters = {
        lang: _this.parameters.language,
        certificatePurpose: _this.parameters.certificatePurpose,
        bulkSigning: _this.parameters.bulkSigning,
        isInitial: isInitial,
        policyId: _this.parameters.policyId,
        policyIds: _this.parameters.policyIds,
      };
      if (_this.parameters.certificatePurpose === 'login') {
        parameters.operation = 'auth';
        parameters.filter = 'AUTH';
      }
      window.hwcrypto.getCertificate(parameters).then(
        function (response) {
          if (_this.updateRequired) {
            return;
          }
          _this.selectedCertificate = response;
          _this.certificateLoaded();
          if (response.log) {
            _this.log(response.log);
          }
        },
        function (err) {
          try {
            var response = JSON.parse(err.message);
          } catch (exception) {
            var response = {};
            response.result = err.message;
          }
          _this.selectedCertificate = null;
          _this.certificateLoaded();

          if (response.result == 'no_implementation') {
            if (
              _this.isFirefox() &&
              (_this.scType == 'lv' ||
                _this.scType == 'ee' ||
                _this.scType == 'e-residency')
            ) {
              iSignApplet.showError(_this.trans('please_enable_plugin'));
            } else {
              iSignApplet.showError(_this.trans('no_implementation'));
            }
          } else if (response.result == 'no_certificates') {
            if (typeof response.driversList == 'undefined') {
              iSignApplet.onCertificateNotFound(null);
            } else {
              iSignApplet.onCertificateNotFound(response.driversList);
            }
          }
          if (response.log) {
            _this.log(response.log);
          }
        },
      );
    },

    certificateLoaded: function () {
      this.updateCertificateValidity();
      this.repaint();
      this.stopLoader();
      if (
        this.selectedCertificate &&
        typeof certificateSelected == 'function'
      ) {
        certificateSelected(
          hexToDer(this.selectedCertificate['hex']),
          this.selectedCertificate,
        );
      }
    },

    updateCertificateValidity: function () {
      if (!this.selectedCertificate) {
        return;
      }
      var validTo = this.selectedCertificate['validTo']; //dd.mm.yyyy
      if (!validTo) {
        this.selectedCertificate.valid = true;
        return;
      }
      var parts = validTo.split('.');
      var validToDate = new Date(parts[2], parts[1], parts[0]);
      var currentDate = new Date();

      if (currentDate < validToDate) {
        this.selectedCertificate.valid = true;
      } else {
        this.selectedCertificate.valid = false;
      }
    },

    sign: function (dtbs, dtbsHash, callback) {
      var _this = this;
      var keys = new Array();
      _this.startLoader(
        _this.trans(
          _this.parameters.certificatePurpose == 'login'
            ? 'authenticating'
            : 'signing',
        ),
      );
      _this.signatureValue = null;

      if (!this.selectedCertificate) {
        _this.stopLoader();
        _this.repaint();
        iSignApplet.showError(
          _this.trans(
            _this.parameters.certificatePurpose == 'login'
              ? 'authentication_failed'
              : 'signing_failed',
          ),
        );
        return;
      }

      var dtbsInHex;
      if (_this.parameters.bulkSigning) {
        var dtbsHexArray = new Array();
        for (var key in dtbs) {
          dtbsHexArray.push(base64ToHex(dtbs[key]));
          keys.push(key);
        }
        dtbsInHex = dtbsHexArray.join(';');
      } else if (hwcrypto.calculateHash == true) {
        dtbsInHex = dtbsHash;
      } else {
        dtbsInHex = base64ToHex(dtbs);
      }

      var signingParameters = {
        hex: this.selectedCertificate['hex'],
        module: this.selectedCertificate['module'],
        slot: this.selectedCertificate['slot'],
      };
      if (
        typeof this.selectedCertificate['isLoginCertificate'] !== 'undefined'
      ) {
        signingParameters.isLoginCertificate =
          this.selectedCertificate['isLoginCertificate'];
      }
      if (
        typeof this.selectedCertificate['isSigningCertificate'] !== 'undefined'
      ) {
        signingParameters.isSigningCertificate =
          this.selectedCertificate['isSigningCertificate'];
      }

      var options = { lang: _this.parameters.language };
      if (_this.parameters.certificatePurpose === 'login') {
        options.operation = 'auth';
        options.filter = 'AUTH';
      }
      window.hwcrypto
        .sign(
          signingParameters,
          { type: _this.parameters.hashAlgorithm, hex: dtbsInHex },
          options,
        )
        .then(
          function (response) {
            _this.stopLoader();
            _this.repaint();

            var result = {};
            if (_this.parameters.bulkSigning) {
              hexArray = response.hex.split(';');

              for (var key = 0; key < hexArray.length; key++) {
                result[keys[key]] = hexToDer(hexArray[key]);
              }
            } else {
              result = hexToDer(response.hex);
            }
            callback(result);
          },
          function (err) {
            try {
              var response = JSON.parse(err.message);
            } catch (exception) {
              var response = {};
              response.result = err.message;
            }
            _this.stopLoader();
            _this.repaint();
            if (response.result == 'pin_blocked') {
              iSignApplet.showError(_this.trans('pin_blocked'));
            } else {
              iSignApplet.showError(
                _this.trans(
                  _this.parameters.certificatePurpose == 'login'
                    ? 'authentication_failed'
                    : 'signing_failed',
                ),
              );
            }
            if (response.log) {
              _this.log(response.log);
            }
            callback(null);
          },
        );
    },
  });
}
// END OF CHROME EXTENSION
// --------------------------------
// START OF JAVA APPLET

function initJavaApplet() {
  $.extend(iSignApplet, {
    certificates: null,
    selectedCertificate: null,
    signRequests: {},
    signingKeys: new Array(),
    appletAccessCounter: 0,
    loading: true,
    mouseIn: false,
    javaAvailable: iSignApplet.javaAvailable
      ? iSignApplet.javaAvailable
      : false,

    start: function () {
      this.initContent();
      this.deploy();
      this.initIEFixes();
      this.initRefreshButton();
      this.initPopover();
      this.initCertificateSelection();

      iSignApplet.startLoader(iSignApplet.trans('loading_applet'));
      this.loadCertificatesWithApplet();
    },

    deploy: function () {
      if ($('#CertificateList').length > 0) {
        return;
      }

      var _this = this;
      _this.runApplet();

      _this.timeout = setTimeout(function () {
        _this.loadingFailed();
      }, _this.parameters.timeout * 1000);
    },

    runApplet: function () {
      var _this = this;

      _this.writeAppletTag(
        {
          codebase: _this.parameters.codebase,
          code: 'CertificateApplet.class',
          archive: _this.parameters.jar + '?' + new Date().getTime(),
          width: '0px',
          height: '0px',
          id: 'CertificateList',
        },
        {
          separate_jvm: false,
          lang: _this.parameters.language, // (lt, en)
          debug: _this.parameters.debug, // (true, false)
          certificatePurpose: _this.parameters.certificatePurpose, // ("sign","login","all")
          bulkSigning: _this.parameters.bulkSigning,
          hashAlgorithm: _this.parameters.hashAlgorithm.toUpperCase(),
        },
      );

      setTimeout(function () {
        if (_this.javaAvailable == false) {
          iSignApplet.showError(iSignApplet.trans('check_java'));
          _this.stopLoader();
        }
      }, _this.parameters.javaCheckTimeout * 1000);
    },

    writeAppletTag: function (attributes, parameters) {
      var startApplet = '<' + 'applet ';
      var params = '';
      var endApplet = '<' + '/' + 'applet' + '>';
      var addCodeAttribute = true;

      if (null == parameters || typeof parameters != 'object') {
        parameters = new Object();
      }

      for (var attribute in attributes) {
        startApplet += ' ' + attribute + '="' + attributes[attribute] + '"';
        if (attribute == 'code') {
          addCodeAttribute = false;
        }
      }

      var codebaseParam = false;
      for (var parameter in parameters) {
        if (parameter == 'codebase_lookup') {
          codebaseParam = true;
        }
        // Originally, parameter 'object' was used for serialized
        // applets, later, to avoid confusion with object tag in IE
        // the 'java_object' was added.  Plugin supports both.
        if (
          parameter == 'object' ||
          parameter == 'java_object' ||
          parameter == 'java_code'
        ) {
          addCodeAttribute = false;
        }
        params +=
          '<param name="' +
          parameter +
          '" value="' +
          parameters[parameter] +
          '"/>';
      }
      if (!codebaseParam) {
        params += '<param name="codebase_lookup" value="false"/>';
      }

      if (addCodeAttribute) {
        startApplet += ' code="dummy"';
      }
      startApplet += '>';

      $('body').append(startApplet + '\n' + params + '\n' + endApplet);
    },

    repaint: function () {
      var popoverContainer = $('#isign-applet .applet-popover ul');
      popoverContainer.html('');
      if (this.certificates) {
        for (var i = 0; i < this.certificates.length; i++) {
          if (this.certificates[i]['id'] == this.selectedCertificate['id']) {
            continue;
          }
          this.printPopoverRow(popoverContainer, this.certificates[i]);
        }
      }

      if (this.certificates && this.certificates.length > 1) {
        this.showDropdownIcon();
      } else {
        this.hideDropdownIcon();
      }

      if (this.certificates) {
        this.setMainText(this.getMainCertificateText(this.selectedCertificate));
        if (this.selectedCertificate['valid'] == false) {
          iSignApplet.showError(this.trans('certificate_not_valid'));
        }
      } else {
        iSignApplet.showError(this.trans('no_devices_' + this.scType));
        this.setMainText(this.trans('certificate_not_selected'));
        this.stopLoader();
      }
    },

    getInstance: function () {
      var certificateList = $('#CertificateList').get(0);
      if (certificateList == null) {
        certificateList = $('#CertificateListIE').get(0);
      }
      return certificateList;
    },

    initCertificateSelection: function () {
      var _this = this;
      $('#isign-applet').delegate('.select-certificate', 'click', function (e) {
        e.preventDefault();
        _this.selectedCertificate = _this.getCertificateById(
          $(this).data('id'),
        );
        $('#isign-applet').find('.applet-popover').hide();
        _this.repaint();
        if (typeof certificateSelected == 'function') {
          certificateSelected(
            atob(_this.selectedCertificate['certificate']),
            _this.selectedCertificate,
          );
        }
      });
    },

    getCertificateById: function (id) {
      if (!this.certificates) {
        return null;
      }
      for (var i = 0; i < this.certificates.length; i++) {
        if (this.certificates[i]['id'] == id) {
          return this.certificates[i];
        }
      }
      return null;
    },

    initRefreshButton: function () {
      var _this = this;
      $('#applet-loader').click(function (e) {
        e.preventDefault();
        if (!_this.loading) {
          if (typeof refreshClicked == 'function') {
            refreshClicked();
          }
          _this.startLoader(_this.trans('searching'));

          _this.loadCertificatesWithApplet();
        }
      });
    },

    loadCertificatesWithApplet: function () {
      var _this = this;
      if (iSignApplet.javaAvailable) {
        try {
          _this.getInstance().loadCertificates();
          _this.startLoader(_this.trans('searching'));
          _this.timeout = setTimeout(function () {
            _this.loadingFailed();
          }, _this.parameters.timeout * 1000);
        } catch (exception) {
          //if applet method is still not accessible
          _this.onAppletNotLoadedYet();
        }
      } else {
        _this.onAppletNotLoadedYet();
      }
    },

    onAppletNotLoadedYet: function () {
      var _this = this;
      _this.appletAccessCounter++;
      if (_this.appletAccessCounter < _this.parameters.timeout) {
        setTimeout(function () {
          _this.loadCertificatesWithApplet();
        }, 1000);
      } else {
        this.loadingFailed();
      }
    },

    loadingFailed: function () {
      this.appletAccessCounter = 0;
      this.setMainText(this.trans('certificate_not_selected'));
      iSignApplet.showError(this.trans('failed_to_load_applet'));
      this.stopLoader();
    },

    certificatesLoaded: function (jsonCertificates) {
      // this one is called from java applet
      clearTimeout(this.timeout);
      if (
        jsonCertificates == null ||
        !$.parseJSON(jsonCertificates)['certificates']
      ) {
        this.certificates = null;
        this.selectedCertificate = null;
        var parsedInfo = $.parseJSON(jsonCertificates);
        iSignApplet.onCertificateNotFound(
          parsedInfo != null && typeof parsedInfo['driversList'] !== 'undefined'
            ? parsedInfo['driversList']
            : null,
        );
        return;
      } else {
        foundCertificates = $.parseJSON(jsonCertificates)['certificates'];
        this.certificates = new Array();

        for (var i = 0; i < foundCertificates.length; i++) {
          if (
            foundCertificates[i]['issuer'] &&
            $.inArray(
              foundCertificates[i]['issuer'],
              this.parameters.disabledIssuers,
            ) > -1
          ) {
            continue;
          }
          this.certificates.push(foundCertificates[i]);
        }

        this.updateCertificatesValidity();

        this.selectedCertificate = null;
        for (var i = 0; i < this.certificates.length; i++) {
          if (
            this.selectedCertificate === null &&
            this.certificates[i]['valid']
          ) {
            this.selectedCertificate = this.certificates[i];
          }
        }
        if (this.selectedCertificate === null) {
          this.selectedCertificate = this.certificates[0];
        }
      }
      this.repaint();
      this.stopLoader();
      if (
        this.selectedCertificate &&
        typeof certificateSelected == 'function'
      ) {
        certificateSelected(
          atob(this.selectedCertificate['certificate']),
          this.selectedCertificate,
        );
      }
    },

    updateCertificatesValidity: function () {
      if (!this.certificates) {
        return;
      }
      for (var i = 0; i < this.certificates.length; i++) {
        var validTo = this.certificates[i]['validTo']; //yyyy.mm.dd.HH.ii.ss
        if (!validTo) continue;
        var parts = validTo.split('.');
        var validToDate = new Date(
          parts[0],
          parts[1],
          parts[2],
          parts[3],
          parts[4],
          parts[5],
        );
        var currentDate = new Date();

        if (currentDate < validToDate) {
          this.certificates[i].valid = true;
        } else {
          this.certificates[i].valid = false;
        }
      }
    },

    sign: function (dtbs, dtbsHash, callback) {
      var _this = this;
      _this.startLoader();

      if (this.parameters.bulkSigning) {
        var dtbsArray = new Array();

        for (var key in dtbs) {
          dtbsArray.push(dtbs[key]);
          _this.signingKeys.push(key);
        }
        dtbs = dtbsArray.join(';');
      }

      var requestId = _this
        .getInstance()
        .signData(
          dtbs,
          _this.selectedCertificate['id'],
          _this.parameters.hashAlgorithm,
        );

      this.signRequests[requestId] = callback;
    },

    signCallback: function (requestId, signedData) {
      var _this = this;
      callback = this.signRequests[requestId];

      this.stopLoader();

      var result;
      if (_this.parameters.bulkSigning && signedData) {
        signedDataArray = signedData.split(';');

        result = {};
        for (var key = 0; key < signedDataArray.length; key++) {
          result[_this.signingKeys[key]] = signedDataArray[key];
        }
      } else {
        result = signedData;
      }
      callback(result);
    },

    initPopover: function () {
      var _this = this;

      $('#isign-applet').delegate(
        '.activate-popover, .applet-popover',
        'mouseenter',
        function () {
          _this.mouseIn = true;
        },
      );
      $('#isign-applet').delegate(
        '.activate-popover, .applet-popover',
        'mouseleave',
        function () {
          _this.mouseIn = false;
        },
      );

      $('#isign-applet').on('click', '.activate-popover', function (e) {
        e.preventDefault();
        if (_this.isPopoverAvailable()) {
          $('#isign-applet').find('.applet-popover').toggle();
        }
      });
    },
  });

  $(document).on('click', function () {
    if (!iSignApplet.mouseIn) {
      $('#isign-applet').find('.applet-popover').hide();
    }
  });
}

// END OF JAVA APPLET
// --------------------------------

if (!window.atob) {
  var Base64 = {
    _keyStr:
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
    encode: function (e) {
      var t = '';
      var n, r, i, s, o, u, a;
      var f = 0;
      e = Base64._utf8_encode(e);
      while (f < e.length) {
        n = e.charCodeAt(f++);
        r = e.charCodeAt(f++);
        i = e.charCodeAt(f++);
        s = n >> 2;
        o = ((n & 3) << 4) | (r >> 4);
        u = ((r & 15) << 2) | (i >> 6);
        a = i & 63;
        if (isNaN(r)) {
          u = a = 64;
        } else if (isNaN(i)) {
          a = 64;
        }
        t =
          t +
          this._keyStr.charAt(s) +
          this._keyStr.charAt(o) +
          this._keyStr.charAt(u) +
          this._keyStr.charAt(a);
      }
      return t;
    },
    decode: function (e) {
      var t = '';
      var n, r, i;
      var s, o, u, a;
      var f = 0;
      e = e.replace(/[^A-Za-z0-9\+\/\=]/g, '');
      while (f < e.length) {
        s = this._keyStr.indexOf(e.charAt(f++));
        o = this._keyStr.indexOf(e.charAt(f++));
        u = this._keyStr.indexOf(e.charAt(f++));
        a = this._keyStr.indexOf(e.charAt(f++));
        n = (s << 2) | (o >> 4);
        r = ((o & 15) << 4) | (u >> 2);
        i = ((u & 3) << 6) | a;
        t = t + String.fromCharCode(n);
        if (u != 64) {
          t = t + String.fromCharCode(r);
        }
        if (a != 64) {
          t = t + String.fromCharCode(i);
        }
      }
      t = Base64._utf8_decode(t);
      return t;
    },
    _utf8_encode: function (e) {
      e = e.replace(/\r\n/g, '\n');
      var t = '';
      for (var n = 0; n < e.length; n++) {
        var r = e.charCodeAt(n);
        if (r < 128) {
          t += String.fromCharCode(r);
        } else if (r > 127 && r < 2048) {
          t += String.fromCharCode((r >> 6) | 192);
          t += String.fromCharCode((r & 63) | 128);
        } else {
          t += String.fromCharCode((r >> 12) | 224);
          t += String.fromCharCode(((r >> 6) & 63) | 128);
          t += String.fromCharCode((r & 63) | 128);
        }
      }
      return t;
    },
    _utf8_decode: function (e) {
      var t = '';
      var n = 0;
      var r = (c1 = c2 = 0);
      while (n < e.length) {
        r = e.charCodeAt(n);
        if (r < 128) {
          t += String.fromCharCode(r);
          n++;
        } else if (r > 191 && r < 224) {
          c2 = e.charCodeAt(n + 1);
          t += String.fromCharCode(((r & 31) << 6) | (c2 & 63));
          n += 2;
        } else {
          c2 = e.charCodeAt(n + 1);
          c3 = e.charCodeAt(n + 2);
          t += String.fromCharCode(
            ((r & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63),
          );
          n += 3;
        }
      }
      return t;
    },
  };

  window.atob = function (base64) {
    return Base64.decode(base64);
  };

  window.btoa = function (bin) {
    return Base64.encode(bin);
  };
}

function hexToBase64(str) {
  return btoa(
    String.fromCharCode.apply(
      null,
      str
        .replace(/\r|\n/g, '')
        .replace(/([\da-fA-F]{2}) ?/g, '0x$1 ')
        .replace(/ +$/, '')
        .split(' '),
    ),
  );
}

function base64ToHex(str) {
  for (
    var i = 0, bin = atob(str.replace(/[ \r\n]+$/, '')), hex = [];
    i < bin.length;
    ++i
  ) {
    var tmp = bin.charCodeAt(i).toString(16);
    if (tmp.length === 1) tmp = '0' + tmp;
    hex[hex.length] = tmp;
  }
  return hex.join('');
}

function hexToDer(text) {
  return hexToBase64(text)
    .match(/.{1,76}/g)
    .join('\n');
}

function appletLoaded() {
  iSignApplet.javaAvailable = true;
  clearTimeout(iSignApplet.timeout); //clear applet loading timeout
}

function extensionLoaded() {
  window.hwcrypto.use('chrome');
  iSignApplet.extensionLoadingStatus = 'loaded';
}

function extensionUnavailable() {
  if (
    (this.scType == 'lv' ||
      this.scType == 'ee' ||
      this.scType == 'e-residency') &&
    _this.parameters.bulkSigning
  ) {
    iSignApplet.showError(
      iSignApplet.trans('no_implementation_for_bulk_signing'),
    );
  } else {
    iSignApplet.showError(iSignApplet.trans('no_implementation'));
  }
  iSignApplet.stopLoader();
  iSignApplet.setMainText(iSignApplet.trans('certificate_not_selected'));
  iSignApplet.extensionLoadingStatus = 'unavailable';
}

function certificatesLoaded(jsonCertificates) {
  iSignApplet.certificatesLoaded(jsonCertificates);
}

function dataSigned(jsonResponse) {
  var obj = JSON.parse(jsonResponse);
  iSignApplet.signCallback(obj.requestId, obj.result ? atob(obj.result) : null);
}

function log(logString) {
  iSignApplet.log(logString);
}

function updateRequired() {
  iSignApplet.showError(iSignApplet.trans('update_required'));
  iSignApplet.setMainText(iSignApplet.trans('certificate_not_selected'));
  iSignApplet.stopLoader();
  iSignApplet.updateRequired = true;
}
