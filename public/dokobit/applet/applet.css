#isign-applet {
  color: #4b4b4b;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 13px;
}
#isign-applet .applet-title {
  margin-right: 5px;
  display: inline-block;
}
#isign-applet ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
#isign-applet .popover-content ul li {
  padding: 3px 20px;
  cursor: pointer;
}
#isign-applet .activate-popover ul li:hover {
  background: #ddd;
}
#isign-applet ul li:hover a {
}
#isign-applet .row > p.applet-title,
#isign-applet .row > a {
  height: 22px;
}
#isign-applet p {
  margin: 0;
  padding: 0;
  display: inline;
}
#isign-applet p.error {
  position: relative;
  background: #ff555a;
  color: #fff;
  text-transform: uppercase;
  padding: 0 10px;
  margin-left: 15px;
  white-space: nowrap;
  display: inline-block;
}
#isign-applet p.error:after {
  right: 100%;
  top: 50%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-right-color: #ff555a;
  border-width: 5px;
  margin-top: -5px;
}
#isign-applet a {
  color: #009ede;
  text-decoration: none;
  position: relative;
}
#isign-applet #selected-certificate a {
  text-decoration: underline;
  white-space: nowrap;
}
#isign-applet .alert-danger {
  color: #ff555a;
}
#isign-applet .arrow {
  width: 0;
  height: 0;
  display: inline-block;
  border-style: solid;
  border-width: 4px 4px 0 4px;
  margin-bottom: 2px;
  margin-right: 10px;
  border-color: #009ede transparent transparent transparent;
}

#isign-applet .activate-popover.active {
  color: #009ede;
  cursor: pointer;
}

#isign-applet .activate-popover.alert-danger.active {
  color: #ff555a;
}

#isign-applet #applet-dropdown-icon {
  display: inline-block;
  margin: 2px 0 2px 0px;
}

@font-face {
  font-family: 'fontello';
  src: "url('font/fontello.eot?24471256')";
  src:
    "url('font/fontello.eot?24471256#iefix') format('embedded-opentype')",
    "url('font/fontello.woff?24471256') format('woff')",
    "url('font/fontello.ttf?24471256') format('truetype')",
    "url('font/fontello.svg?24471256#fontello') format('svg')";
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontello';
    src: "url('../font/fontello.svg?24471256#fontello') format('svg')";
  }
}
*/

#isign-applet [class^='icon-']:before,
#isign-applet [class*=' icon-']:before {
  font-family: 'fontello';
  font-style: normal;
  font-weight: normal;
  speak: none;
  color: #b7b7b7;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: 0.2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes */
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: 0.2em;

  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

#isign-applet .icon-arrows-cw:before {
  content: '\e800';
} /* '' */

/*
   Animation example, for spinners
*/
#isign-applet .animate-spin {
  -moz-animation: spin 2s infinite linear;
  -o-animation: spin 2s infinite linear;
  -webkit-animation: spin 2s infinite linear;
  animation: spin 2s infinite linear;
  display: inline-block;
}

/*
   Animation example, for spinners
*/
#isign-applet .animate-spin {
  -moz-animation: spin 2s infinite linear;
  -o-animation: spin 2s infinite linear;
  -webkit-animation: spin 2s infinite linear;
  animation: spin 2s infinite linear;
  display: inline-block;
}
@-moz-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-webkit-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-o-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-ms-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

#isign-applet .applet-popover {
  position: absolute;
  z-index: 1060;
  display: none;
  line-height: 1.5;
  text-align: left;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  white-space: normal;
}

#isign-applet .applet-popover.top {
  margin-top: -10px;
}

#isign-applet .applet-popover.right {
  margin-left: 10px;
}

#isign-applet .applet-popover.bottom {
  margin-top: 10px;
}

#isign-applet .applet-popover.left {
  margin-left: -10px;
}

#isign-applet .popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0;
}

#isign-applet .applet-popover-content {
  padding: 8px 0;
}

#isign-applet .applet-popover > .arrow,
#isign-applet .applet-popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

#isign-applet .applet-popover > .arrow {
  border-width: 11px;
}

#isign-applet .applet-popover > .arrow:after {
  border-width: 10px;
  content: '';
}

#isign-applet .applet-popover.top > .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px;
}

#isign-applet .applet-popover.top > .arrow:after {
  content: ' ';
  bottom: 1px;
  margin-left: -10px;
  border-bottom-width: 0;
  border-top-color: #fff;
}

#isign-applet .applet-popover.right > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999;
  border-right-color: rgba(0, 0, 0, 0.25);
}

#isign-applet .applet-popover.right > .arrow:after {
  content: ' ';
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #fff;
}

#isign-applet .applet-popover.bottom > .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}

#isign-applet .applet-popover.bottom > .arrow:after {
  content: ' ';
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #fff;
}

#isign-applet .applet-popover.left > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999;
  border-left-color: rgba(0, 0, 0, 0.25);
}

#isign-applet .applet-popover.left > .arrow:after {
  content: ' ';
  right: 1px;
  border-right-width: 0;
  border-left-color: #fff;
  bottom: -10px;
}

#isign-applet .clearfix:before,
.clearfix:after {
  content: ' ';
  display: table;
}

#isign-applet .clearfix:after {
  clear: both;
}

#isign-applet .clearfix {
  height: 0px;
}

#isign-applet #applet-loader {
  display: inline-block;
  height: 12px;
}

#isign-applet .loader-img {
  background: "url('img/refresh.png')" no-repeat;
  float: left;
  height: 12px;
  margin-left: 10px;
  margin-top: 2px;
  width: 14px;
}

#isign-applet .loader-img-ie {
  background: "url('img/loader.gif')" no-repeat;
  float: right;
  height: 16px;
  margin-left: 10px;
  width: 70px;
}

#deployJavaPlugin {
  display: none;
}

/* NEW PART START HERE */

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src:
    local('Open Sans Light'),
    local('OpenSans-Light'),
    'url(https://fonts.gstatic.com/s/opensans/v13/DXI1ORHCpsQm3Vp6mXoaTQ7aC6SjiAOpAWOKfJDfVRY.woff2)'
      format('woff2');
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src:
    local('Open Sans Light'),
    local('OpenSans-Light'),
    'url(https://fonts.gstatic.com/s/opensans/v13/DXI1ORHCpsQm3Vp6mXoaTRdwxCXfZpKo5kWAx_74bHs.woff2)'
      format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src:
    local('Open Sans Light'),
    local('OpenSans-Light'),
    'url(https://fonts.gstatic.com/s/opensans/v13/DXI1ORHCpsQm3Vp6mXoaTZ6vnaPZw6nYDxM4SVEMFKg.woff2)'
      format('woff2');
  unicode-range: U+1F00-1FFF;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src:
    local('Open Sans Light'),
    local('OpenSans-Light'),
    'url(https://fonts.gstatic.com/s/opensans/v13/DXI1ORHCpsQm3Vp6mXoaTfy1_HTwRwgtl1cPga3Fy3Y.woff2)'
      format('woff2');
  unicode-range: U+0370-03FF;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src:
    local('Open Sans Light'),
    local('OpenSans-Light'),
    'url(https://fonts.gstatic.com/s/opensans/v13/DXI1ORHCpsQm3Vp6mXoaTfgrLsWo7Jk1KvZser0olKY.woff2)'
      format('woff2');
  unicode-range: U+0102-0103, U+1EA0-1EF1, U+20AB;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src:
    local('Open Sans Light'),
    local('OpenSans-Light'),
    'url(https://fonts.gstatic.com/s/opensans/v13/DXI1ORHCpsQm3Vp6mXoaTYjoYw3YTyktCCer_ilOlhE.woff2)'
      format('woff2');
  unicode-range:
    U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src:
    local('Open Sans Light'),
    local('OpenSans-Light'),
    'url(https://fonts.gstatic.com/s/opensans/v13/DXI1ORHCpsQm3Vp6mXoaTRampu5_7CjHW5spxoeN3Vs.woff2)'
      format('woff2');
  unicode-range:
    U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src:
    local('Open Sans'),
    local('OpenSans'),
    'url(https://fonts.gstatic.com/s/opensans/v13/K88pR3goAWT7BTt32Z01m4X0hVgzZQUfRDuZrPvH3D8.woff2)'
      format('woff2');
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src:
    local('Open Sans'),
    local('OpenSans'),
    'url(https://fonts.gstatic.com/s/opensans/v13/RjgO7rYTmqiVp7vzi-Q5UYX0hVgzZQUfRDuZrPvH3D8.woff2)'
      format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src:
    local('Open Sans'),
    local('OpenSans'),
    'url(https://fonts.gstatic.com/s/opensans/v13/LWCjsQkB6EMdfHrEVqA1KYX0hVgzZQUfRDuZrPvH3D8.woff2)'
      format('woff2');
  unicode-range: U+1F00-1FFF;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src:
    local('Open Sans'),
    local('OpenSans'),
    'url(https://fonts.gstatic.com/s/opensans/v13/xozscpT2726on7jbcb_pAoX0hVgzZQUfRDuZrPvH3D8.woff2)'
      format('woff2');
  unicode-range: U+0370-03FF;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src:
    local('Open Sans'),
    local('OpenSans'),
    'url(https://fonts.gstatic.com/s/opensans/v13/59ZRklaO5bWGqF5A9baEEYX0hVgzZQUfRDuZrPvH3D8.woff2)'
      format('woff2');
  unicode-range: U+0102-0103, U+1EA0-1EF1, U+20AB;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src:
    local('Open Sans'),
    local('OpenSans'),
    'url(https://fonts.gstatic.com/s/opensans/v13/u-WUoqrET9fUeobQW7jkRYX0hVgzZQUfRDuZrPvH3D8.woff2)'
      format('woff2');
  unicode-range:
    U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src:
    local('Open Sans'),
    local('OpenSans'),
    'url(https://fonts.gstatic.com/s/opensans/v13/cJZKeOuBrn4kERxqtaUH3ZBw1xU1rKptJj_0jans920.woff2)'
      format('woff2');
  unicode-range:
    U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}
/*
SCSS variables are information about icon's compiled state, stored under its original file name

.icon-home {
  width: $icon-home-width;
}

The large array-like variables contain all information about a single icon
$icon-home: x y offset_x offset_y width height total_width total_height image_path;

At the bottom of this section, we provide information about the spritesheet itself
$spritesheet: width height image $spritesheet-sprites;
*/
/*
The provided mixins are intended to be used with the array-like variables

.icon-home {
  @include sprite-width($icon-home);
}

.icon-email {
  @include sprite($icon-email);
}
*/
/*
The `sprites` mixin generates identical output to the CSS template
  but can be overridden inside of SCSS

@include sprites($spritesheet-sprites);
*/
/*
The `retina-sprites` mixin generates a CSS rule and media query for retina groups
  This yields the same output as CSS retina template but can be overridden in SCSS

@include retina-sprites($retina-groups);
*/
#isign-applet {
  font-family: 'Open Sans', sans-serif;
}
#isign-applet .text-muted {
  color: #9a9a9a;
}
#isign-applet .icon-container {
  background: #b5bbc8;
  border-radius: 100%;
  display: inline-block;
  height: 28px;
  position: relative;
  text-align: center;
  top: 0;
  vertical-align: middle;
  width: 28px;
}
#isign-applet .small-icon,
#isign-applet .help-icon,
#isign-applet .icon,
#isign-applet .input-holder__alert__icon {
  -moz-transition: fill 0.3s ease-in-out;
  -o-transition: fill 0.3s ease-in-out;
  -webkit-transition: fill 0.3s ease-in-out;
  transition: fill 0.3s ease-in-out;
  display: inline-block;
  height: 24px;
  vertical-align: middle;
  width: 24px;
}
#isign-applet .small-icon,
#isign-applet .help-icon {
  height: 14px;
  width: 14px;
}
#isign-applet .medium-icon {
  -moz-transition: fill 0.3s ease-in-out;
  -o-transition: fill 0.3s ease-in-out;
  -webkit-transition: fill 0.3s ease-in-out;
  transition: fill 0.3s ease-in-out;
  display: inline-block;
  height: 18px;
  vertical-align: middle;
  width: 18px;
}
#isign-applet .residency-radio {
  padding-left: 0;
  list-style: none;
  margin: 0 0 0 -5px;
}
#isign-applet .residency-radio li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px;
}
#isign-applet * {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
#isign-applet *::-moz-selection {
  color: #fff;
  background-color: #009ede;
}
#isign-applet *::selection {
  color: #fff;
  background-color: #009ede;
}
#isign-applet *:before,
#isign-applet *:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
#isign-applet input,
#isign-applet button,
#isign-applet select,
#isign-applet textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
#isign-applet a {
  color: #009ede;
  text-decoration: none;
}
#isign-applet a:hover,
#isign-applet a:focus {
  color: #008fc9;
  text-decoration: underline;
}
#isign-applet html {
  color: #535353;
  font-family: 'Open Sans', sans-serif;
  font-size: 13px;
}
#isign-applet hr {
  border: 0;
  border-top: 1px solid #dcdedf;
  margin: 10px 0;
}
#isign-applet p {
  margin: 0;
}
#isign-applet h1 {
  color: #535353;
  font-size: 30px;
  font-weight: 200;
  margin: 0 0 8px;
  text-align: center;
}
#isign-applet h2 {
  font-size: 20px;
  font-weight: 200;
  line-height: 34px;
  margin-bottom: 15px;
  margin-top: 38px;
}
#isign-applet h3 {
  font-size: 16px;
  font-weight: 200;
  line-height: 17px;
  margin: 0;
}
#isign-applet h4 {
  color: #333;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  margin: 0;
}
#isign-applet :focus {
  outline: none;
}
#isign-applet .number {
  color: #009ede;
  margin-right: 15px;
}
#isign-applet .text-center {
  text-align: center;
}
#isign-applet .list-unstyled {
  padding-left: 0;
  list-style: none;
}
#isign-applet .collapse {
  display: none;
}
#isign-applet .collapse.in {
  display: block;
}
#isign-applet tr.collapse.in {
  display: table-row;
}
@media (max-width: 767px) {
  #isign-applet .m-t-10-xs {
    margin-top: 10px;
  }
}
#isign-applet .m-b-10 {
  margin-bottom: 10px;
}
#isign-applet .m-t-25 {
  margin-top: 25px;
}
#isign-applet .collapsing {
  -moz-transition-property: height, visibility;
  -o-transition-property: height, visibility;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -moz-transition-duration: 0.35s;
  -o-transition-duration: 0.35s;
  -webkit-transition-duration: 0.35s;
  transition-duration: 0.35s;
  -moz-transition-timing-function: ease;
  -o-transition-timing-function: ease;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
  height: 0;
  overflow: hidden;
  position: relative;
}
#isign-applet .text-right {
  text-align: right;
}
#isign-applet .medium-icon {
  position: relative;
  top: -2px;
}
#isign-applet .medium-icon--success {
  fill: #3fc380;
}
#isign-applet .medium-icon--error {
  fill: #f45c5a;
}
#isign-applet .medium-icon--warning {
  fill: #f6b43c;
}
#isign-applet .medium-icon--primary {
  fill: #009ede;
}
#isign-applet .small-icon {
  position: relative;
  top: -2px;
}
#isign-applet .help-icon {
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  fill: #009ede;
  position: relative;
  top: -1px;
  margin-left: 3px;
}
#isign-applet .help-icon:hover {
  fill: #008fc9;
}
#isign-applet .simple-hide {
  display: none !important;
}
#isign-applet .clearfix:before,
#isign-applet .clearfix:after {
  content: ' ';
  display: table;
}
#isign-applet .clearfix:after {
  clear: both;
}
@media (min-width: 768px) and (max-width: 991px) {
  #isign-applet .clearfix-sm:before,
  #isign-applet .clearfix-sm:after {
    content: ' ';
    display: table;
  }
  #isign-applet .clearfix-sm:after {
    clear: both;
  }
}
#isign-applet .counter {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid #009ede;
  border-radius: 100%;
  color: #009ede;
  cursor: default;
  display: inline-block;
  font-size: 10px;
  height: 20px;
  line-height: 19px;
  text-align: center;
  vertical-align: middle;
  width: 20px;
}
#isign-applet ._is_hidden {
  display: none;
}
#isign-applet .signing-spinner {
  -moz-animation: loader 1.1s infinite ease;
  -webkit-animation: loader 1.1s infinite ease;
  animation: loader 1.1s infinite ease;
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  border-radius: 50%;
  display: inline-block;
  font-size: 3px;
  height: 1em;
  margin: 0 15px;
  position: relative;
  text-indent: -9999em;
  top: -2px;
  width: 1em;
}
#isign-applet .row._is_flex {
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
}
#isign-applet .row._is_flex:before,
#isign-applet .row._is_flex:after {
  display: none;
}
#isign-applet .row._is_flex > [class*='col-'] {
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column;
  flex-direction: column;
}
#isign-applet .row._is_flex > [class*='col-'].block {
  display: block;
}
#isign-applet .row._is_flex > [class*='col-'].center {
  -webkit-justify-content: center;
  justify-content: center;
}
#isign-applet .link {
  color: #009ede;
  cursor: pointer;
}
#isign-applet .link:hover,
#isign-applet .link:focus {
  text-decoration: underline;
}
#isign-applet .list-extra-spaced {
  padding-left: 0;
  list-style: none;
}
#isign-applet .list-extra-spaced__item {
  padding-bottom: 30px;
}
#isign-applet .list-spaced {
  padding-left: 0;
  list-style: none;
}
#isign-applet .list-spaced__item {
  padding-bottom: 15px;
}
#isign-applet .input-holder {
  position: relative;
}
#isign-applet .input-holder--required:after {
  bottom: 7px;
  content: '*';
  line-height: 13px;
  position: absolute;
  right: 10px;
}
#isign-applet .input-holder label {
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  display: block;
  line-height: normal;
  padding-bottom: 8px;
}
#isign-applet .input-holder._has_error label {
  color: #f45c5a;
}
#isign-applet .input-holder._has_error input {
  background: rgba(244, 92, 90, 0.1);
  border-color: #f45c5a;
  box-shadow: 0 0 0 1px #f45c5a;
}
#isign-applet .input-holder__alert {
  border-collapse: separate;
  border-radius: 5px;
  color: #fff;
  display: table;
  font-size: 14px;
  line-height: 20px;
  margin: 18px 0;
  padding: 15px;
  width: 100%;
}
#isign-applet .input-holder__alert--success {
  background: #3fc380;
}
#isign-applet .input-holder__alert--error {
  background: #f45c5a;
}
#isign-applet .input-holder__alert--warning {
  background: #f6b43c;
}
#isign-applet .input-holder__alert--primary {
  background: #009ede;
}
#isign-applet .input-holder__alert._is_hidden {
  display: none;
}
#isign-applet .input-holder__alert__icon {
  fill: #fff;
}
#isign-applet .input-holder__alert__text {
  padding: 0 14px;
}
#isign-applet .input-holder__alert__text > ul {
  padding-left: 0;
  list-style: none;
}
#isign-applet .input-holder__alert__text > ul li {
  word-break: break-all;
}
#isign-applet .input-holder__alert__cancel {
  cursor: pointer;
  display: table-cell;
  font-size: 27px;
  font-weight: 200;
  opacity: 0.5;
  vertical-align: middle;
}
#isign-applet .input-holder__alert__cancel:after {
  content: '\00d7';
}
#isign-applet .input-holder__alert > * {
  display: table-cell;
  vertical-align: middle;
}
#isign-applet .input-holder._has_warning label {
  color: #f6b43c;
}
#isign-applet .input-holder._has_warning input {
  background: rgba(246, 180, 60, 0.1);
  border-color: #f6b43c;
  box-shadow: 0 0 0 1px #f6b43c;
}
#isign-applet .input-holder--search:before {
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background: 'url(img/ic-search-grey.svg)';
  content: '';
  height: 11px;
  left: 22px;
  position: absolute;
  width: 11px;
  top: 50%;
}
#isign-applet .input-holder--search input {
  height: 40px;
  padding: 9px 11px 9px 46px;
}
#isign-applet .input-holder label {
  color: #9a9a9a;
}
#isign-applet .input-holder--required:after {
  color: #009ede;
}
#isign-applet .residency-radio {
  padding-top: 10px;
}
#isign-applet .residency-radio__item__input {
  -webkit-appearance: none;
  background: transparent;
  border: 0;
  cursor: pointer;
  height: 16px;
  margin-right: 10px;
  position: relative;
  top: 0;
  width: 16px;
}
#isign-applet .residency-radio__item__input:before {
  border-style: solid;
  border-width: 2px;
  content: '';
  height: 16px;
  left: 0;
  position: absolute;
  top: 3px;
  width: 16px;
  z-index: 1;
}
#isign-applet .residency-radio__item__input:checked:before {
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  border-right-style: none;
  border-top-style: none;
  height: 8px;
  width: 14px;
}
#isign-applet .residency-radio__item__input:after {
  background: inherit;
  content: '';
  cursor: pointer;
  height: 16px;
  left: 0;
  position: absolute;
  top: 3px;
  width: 16px;
}
#isign-applet .residency-radio__item__label {
  cursor: pointer;
}
#isign-applet .residency-radio__item__input:before {
  background: #fff;
  border-color: #fff;
  border-radius: 100%;
  height: 12px;
  left: 2px;
  top: 5px;
  width: 12px;
}
#isign-applet .residency-radio__item__input:after {
  background: #e5e5e5;
  border-radius: 100%;
}
#isign-applet .residency-radio__item__input:checked:before {
  background: #009ede;
  border-style: solid;
  height: 12px;
  width: 12px;
}
#isign-applet .residency-radio__container {
  cursor: pointer;
  display: block;
  height: 100%;
}
#isign-applet .residency-radio__item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -moz-transition:
    left 0.3s ease-in-out,
    top 0.3s ease-in-out,
    background 0.3s ease-in-out,
    box-shadow 0.3s ease-in-out;
  -o-transition:
    left 0.3s ease-in-out,
    top 0.3s ease-in-out,
    background 0.3s ease-in-out,
    box-shadow 0.3s ease-in-out;
  -webkit-transition:
    left 0.3s ease-in-out,
    top 0.3s ease-in-out,
    background 0.3s ease-in-out,
    box-shadow 0.3s ease-in-out;
  transition:
    left 0.3s ease-in-out,
    top 0.3s ease-in-out,
    background 0.3s ease-in-out,
    box-shadow 0.3s ease-in-out;
  border: 1px solid #dcdedf;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  height: 100px;
  margin: 10px;
  padding-top: 7px;
  position: relative;
  text-align: center;
  vertical-align: top;
  width: 100px;
}
#isign-applet .residency-radio__item:hover,
#isign-applet .residency-radio__item:focus,
#isign-applet .residency-radio__item._is_selected {
  border: 1px solid #009ede;
  box-shadow: 0 0 0 1px #009ede;
}
#isign-applet .residency-radio__item__flag {
  display: block;
  margin: 15px auto 10px;
}
#isign-applet .residency-radio__item__flag--lt {
  background-image: 'url(img/isign-applet-spritesheet.png)';
  background-position: -80px -32px;
  width: 20px;
  height: 12px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  #isign-applet .residency-radio__item__flag--lt {
    background-image: 'url(img/<EMAIL>)';
    background-size: 100px 85px;
  }
}
#isign-applet .residency-radio__item__flag--ee,
#isign-applet .residency-radio__item__flag--e-residency {
  background-image: 'url(img/isign-applet-spritesheet.png)';
  background-position: -40px 0px;
  width: 20px;
  height: 13px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  #isign-applet .residency-radio__item__flag--ee,
  #isign-applet .residency-radio__item__flag--e-residency {
    background-image: 'url(img/<EMAIL>)';
    background-size: 100px 85px;
  }
}
#isign-applet .residency-radio__item__flag--lv {
  background-image: 'url(img/isign-applet-spritesheet.png)';
  background-position: 0px -73px;
  width: 20px;
  height: 12px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  #isign-applet .residency-radio__item__flag--lv {
    background-image: 'url(img/<EMAIL>)';
    background-size: 100px 85px;
  }
}
#isign-applet .residency-radio__item__flag--fi {
  background-image: 'url(img/isign-applet-spritesheet.png)';
  background-position: 0px -40px;
  width: 20px;
  height: 13px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  #isign-applet .residency-radio__item__flag--fi {
    background-image: 'url(img/<EMAIL>)';
    background-size: 100px 85px;
  }
}
#isign-applet .residency-radio__item__flag--be {
  background-image: 'url(img/isign-applet-spritesheet.png)';
  background-position: -80px 0px;
  width: 20px;
  height: 12px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  #isign-applet .residency-radio__item__flag--be {
    background-image: 'url(img/<EMAIL>)';
    background-size: 100px 85px;
  }
}
#isign-applet .residency-radio__item__flag--is {
  background-image: 'url(img/isign-applet-spritesheet.png)';
  background-position: -40px 45px;
  width: 20px;
  height: 13px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  #isign-applet .residency-radio__item__flag--is {
    background-image: 'url(img/<EMAIL>)';
    background-size: 100px 85px;
  }
}
#isign-applet .residency-radio__item__input {
  margin: 0;
}
#isign-applet .residency-radio__item__label {
  font-size: 13px;
}
#isign-applet .residency-radio__item.warning {
  border: 2px solid #f6b43c;
  background: #fef7ec;
}
#isign-applet .residency-radio__item.warning:hover,
#isign-applet .residency-radio__item.warning:focus,
#isign-applet .residency-radio__item.warning._is_selected {
  border: 1px solid #f6b43c;
  box-shadow: 0 0 0 1px #f6b43c;
}
#isign-applet .residency-radio__item.error {
  border: 2px solid #f45c5a;
  background: #fdeeee;
}
#isign-applet .residency-radio__item.error:hover,
#isign-applet .residency-radio__item.error:focus,
#isign-applet .residency-radio__item.error._is_selected {
  border: 1px solid #f45c5a;
  box-shadow: 0 0 0 1px #f45c5a;
}
#isign-applet .input-holder__alert a {
  color: #fff;
  text-decoration: underline;
  cursor: pointer;
}

.select-select {
  display: none;
  /* For when we are on a small touch device and want to use native controls */
  pointer-events: none;
  position: absolute;
  opacity: 0;
}

.select-element,
.select-element:after,
.select-element:before,
.select-element *,
.select-element *:after,
.select-element *:before {
  box-sizing: border-box;
}

.select-element {
  position: absolute;
  display: none;
}
.select-element.select-open {
  display: block;
}

.select-theme-chosen {
  font-family: 'Helvetica Neue', sans-serif;
  font-size: 13px;
}
.select-theme-chosen,
.select-theme-chosen *,
.select-theme-chosen *:after,
.select-theme-chosen *:before {
  box-sizing: border-box;
}

.select.select-theme-chosen {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.select.select-theme-chosen .select-content {
  border-radius: 5px;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.2);
  background: #fff;
  color: #444;
  overflow: auto;
  max-width: 248px;
  max-height: 248px;
  -webkit-overflow-scrolling: touch;
}
@media (max-width: 372px), (max-height: 372px) {
  .select.select-theme-chosen .select-content {
    max-width: 155px;
    max-height: 155px;
  }
}
.select.select-theme-chosen .select-options {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  margin: 0;
  padding: 0;
}
.select.select-theme-chosen .select-options .select-option {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  position: relative;
  list-style: none;
  margin: 0;
  line-height: 19px;
  padding: 6px 11px 6px 30px;
  display: block;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.select.select-theme-chosen .select-options .select-option:hover,
.select.select-theme-chosen
  .select-options
  .select-option.select-option-highlight {
  background-image: linear-gradient(#3875d7 20%, #2a62bc 90%);
  background-color: #3875d7;
  color: #fff;
}
.select.select-theme-chosen .select-options .select-option:first-child {
  border-radius: 5px 5px 0 0;
}
.select.select-theme-chosen .select-options .select-option:last-child {
  border-radius: 0 0 5px 5px;
}

.select-target.select-theme-chosen {
  display: inline-block;
  vertical-align: middle;
  *vertical-align: auto;
  *zoom: 1;
  *display: inline;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  border-radius: 5px;
  box-shadow:
    0 0 3px #fff inset,
    0 1px 1px rgba(0, 0, 0, 0.1);
  background-image: linear-gradient(
    to bottom,
    #fff 20%,
    #f6f6f6 50%,
    #eee 52%,
    #f4f4f4 100%
  );
  position: relative;
  padding: 3px 30px 2px 11px;
  background: #f6f6f6;
  border: 1px solid #aaa;
  cursor: pointer;
  color: #444;
  text-decoration: none;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 24px;
}
.select-target.select-theme-chosen.select-target-focused,
.select-target.select-theme-chosen.select-target-focused:focus {
  border-color: #5897fb;
  outline: none;
}
.select-target.select-theme-chosen b {
  position: absolute;
  right: 13px;
  top: 0;
  bottom: 1px;
  margin: auto;
  height: 16px;
  width: 26px;
}
.select-target.select-theme-chosen b:before,
.select-target.select-theme-chosen b:after {
  content: '';
  display: block;
  position: absolute;
  margin: auto;
  right: 0;
  height: 0;
  width: 0;
  border: 3px solid transparent;
}
.select-target.select-theme-chosen b:before {
  top: 0;
  border-bottom-color: inherit;
}
.select-target.select-theme-chosen b:after {
  bottom: 0;
  border-top-color: inherit;
}

.drop-content {
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.24);
  border-radius: 2px;
  padding: 3px;
  font-size: 13px;
  background: #fff;
  border-radius: 5px;
}
.drop-content .drop-content-inner {
  border: 1px solid #dbdbdb;
  padding: 14px;
  background: #fff;
  overflow: hidden;
  width: 280px;
}
.drop-content .drop-content-inner .title {
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 12px;
  font-weight: normal;
  line-height: 1;
}
.drop-content .drop-content-inner p:last-child {
  margin-bottom: 0;
}

.drop-element {
  -moz-transition:
    opacity 0.3s ease-in-out,
    top 0.3s ease-in-out;
  -o-transition:
    opacity 0.3s ease-in-out,
    top 0.3s ease-in-out;
  -webkit-transition:
    opacity 0.3s ease-in-out,
    top 0.3s ease-in-out;
  transition:
    opacity 0.3s ease-in-out,
    top 0.3s ease-in-out;
  opacity: 0;
  top: -10px;
  z-index: 3;
}
.drop-element .drop-content {
  -moz-transition: -moz-transform 0.3s ease-in-out;
  -o-transition: -o-transform 0.3s ease-in-out;
  -webkit-transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
}
.drop-element.drop-after-open {
  opacity: 1;
}
.drop-element.drop-after-open .drop-content {
  -moz-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -webkit-transform: translateY(10px);
  transform: translateY(10px);
}

.select-element {
  z-index: 3;
}

.isign-select-theme-certificate {
  color: #535353;
  font-size: 13px;
  margin-right: 11px;
  padding-right: 18px;
  position: relative;
  z-index: 3;
}
.isign-select-theme-certificate .select-content {
  -moz-animation-name: fadeInDown;
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}
.isign-select-theme-certificate.select-target:hover,
.isign-select-theme-certificate.select-target:focus {
  color: inherit;
  text-decoration: none;
}
.isign-select-theme-certificate--inline {
  padding-left: 20px;
}
.isign-select-theme-certificate [data-value]:before {
  content: '';
  display: inline-block;
  margin-right: 10px;
}
.isign-select-theme-certificate b {
  bottom: 0;
  height: 5px;
  margin: auto;
  position: absolute;
  right: 0;
  top: -10px;
  width: 10px;
}
.isign-select-theme-certificate b:after,
.isign-select-theme-certificate b:before {
  border: 5px solid transparent;
  content: '';
  display: block;
  height: 0;
  margin: auto;
  position: absolute;
  right: 0;
  width: 0;
}
.isign-select-theme-certificate b:before {
  border-top-color: #009ede;
  top: 5px;
}

.isign-select-theme-certificate .select-content {
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.24);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.24);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.24);
  background: #fff;
  border-radius: 5px;
  padding: 3px 16px;
}
.isign-select-theme-certificate .select-content .select-options {
  margin: 0;
  padding: 0;
}
.isign-select-theme-certificate .select-content .select-option {
  cursor: pointer;
  line-height: 29px;
  list-style: none;
}
.isign-select-theme-certificate
  .select-content
  .select-option.select-option-selected:after {
  background-image: 'url(img/isign-applet-spritesheet.png)';
  background-position: 0px 0px;
  width: 20px;
  height: 20px;
  content: '';
  display: inline-block;
  margin-left: 5px;
  position: relative;
  top: 4px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .isign-select-theme-certificate
    .select-content
    .select-option.select-option-selected:after {
    background-image: 'url(img/<EMAIL>)';
    background-size: 100px 53px;
  }
}
.isign-select-theme-certificate .select-content .select-option:hover,
.isign-select-theme-certificate .select-content .select-option:focus {
  text-decoration: underline;
}

.isign-select-theme-certificate .select-heading {
  color: #9a9a9a;
  font-size: 13px;
  padding: 14px 28px 0;
  text-transform: uppercase;
}
