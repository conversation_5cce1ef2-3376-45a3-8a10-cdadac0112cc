import '../src/app/styles/index.css';

import type { Preview } from '@storybook/react';
import * as React from 'react';

import { Toaster } from '../src/shared/components/ui/sonner';
const preview: Preview = {
  decorators: [
    (Story) => {
      return (
        <div>
          <Story />
          <Toaster />
        </div>
      );
    },
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
