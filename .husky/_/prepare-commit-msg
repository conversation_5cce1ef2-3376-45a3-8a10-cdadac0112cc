#!/bin/sh

if [ "$LEFTHOOK_VERBOSE" = "1" -o "$LEFTHOOK_VERBOSE" = "true" ]; then
  set -x
fi

if [ "$LEFTHOOK" = "0" ]; then
  exit 0
fi

call_lefthook()
{
  if test -n "$LEFTHOOK_BIN"
  then
    "$LEFTHOOK_BIN" "$@"
  elif lefthook -h >/dev/null 2>&1
  then
    lefthook "$@"
  else
    dir="$(git rev-parse --show-toplevel)"
    if test -f "$dir/node_modules/lefthook/bin/index.js"
    then
      "$dir/node_modules/lefthook/bin/index.js" "$@"
    else
      osArch=$(uname | tr '[:upper:]' '[:lower:]')
      cpuArch=$(uname -m | sed 's/aarch64/arm64/')
      if test -f "$dir/node_modules/@evilmartians/lefthook/bin/lefthook_${osArch}_${cpuArch}/lefthook"
      then
        "$dir/node_modules/@evilmartians/lefthook/bin/lefthook_${osArch}_${cpuArch}/lefthook" "$@"
      elif test -f "$dir/node_modules/@evilmartians/lefthook-installer/bin/lefthook_${osArch}_${cpuArch}/lefthook"
      then
        "$dir/node_modules/@evilmartians/lefthook-installer/bin/lefthook_${osArch}_${cpuArch}/lefthook" "$@"
      
      elif bundle exec lefthook -h >/dev/null 2>&1
      then
        bundle exec lefthook "$@"
      elif yarn lefthook -h >/dev/null 2>&1
      then
        yarn lefthook "$@"
      elif pnpm lefthook -h >/dev/null 2>&1
      then
        pnpm lefthook "$@"
      elif swift package plugin lefthook >/dev/null 2>&1
      then
        swift package --disable-sandbox plugin lefthook "$@"
      elif command -v mint >/dev/null 2>&1
      then
        mint run csjones/lefthook-plugin "$@"
      elif command -v npx >/dev/null 2>&1
      then
        npx lefthook "$@"
      else
        echo "Can't find lefthook in PATH"
      fi
    fi
  fi
}

call_lefthook run "prepare-commit-msg" "$@"
